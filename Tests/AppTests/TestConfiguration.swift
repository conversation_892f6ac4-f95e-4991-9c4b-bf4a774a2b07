//
//  TestConfiguration.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import XCTest
import Vapor
import Fluent
import FluentPostgresDriver
@testable import App

// MARK: - Test Configuration
extension Application {
    static func testable() throws -> Application {
        let app = Application(.testing)
        try configure(app)
        return app
    }
}

// MARK: - Test Utilities
struct TestUtilities {
    
    // MARK: - User Creation
    static func createTestUser(on db: Database, firstName: String = "Test", lastName: String = "User", email: String? = nil) throws -> User {
        let user = User(
            firstName: firstName,
            lastName: lastName,
            email: email ?? "\(firstName.lowercased()).\(lastName.lowercased())@test.com"
        )
        try user.save(on: db).wait()
        return user
    }
    
    // MARK: - Notification Preferences Creation
    static func createTestPreferences(for user: User, on db: Database) throws -> UserNotificationPreferences {
        let preferences = UserNotificationPreferences(
            userID: try user.requireID(),
            smsEnabled: true,
            smsPhoneNumber: "+1234567890",
            emailEnabled: true,
            emailAddress: user.email,
            pushEnabled: true,
            browserEnabled: true,
            inAppEnabled: true,
            reminderHours: [24, 48],
            quietHoursStart: 22,
            quietHoursEnd: 8,
            timezone: "America/New_York"
        )
        try preferences.save(on: db).wait()
        return preferences
    }
    
    // MARK: - Task Creation
    static func createTestTask(creator: User, assignee: User? = nil, on db: Database, dueInHours: Int = 24) throws -> TaskModel {
        let dueDate = Date().addingTimeInterval(Double(dueInHours * 3600))
        let task = TaskModel(
            title: "Test Task",
            description: "Test task description",
            dueAtEpoc: Int(dueDate.timeIntervalSince1970),
            creatorID: try creator.requireID(),
            assigneeID: try (assignee ?? creator).requireID()
        )
        try task.save(on: db).wait()
        return task
    }
    
    // MARK: - Goal Creation
    static func createTestGoal(on db: Database, dueInHours: Int = 48) throws -> Goal {
        // First create a care plan
        let member = try createTestUser(on: db, firstName: "Member", lastName: "Test")
        let carePlan = CarePlan(
            memberID: try member.requireID(),
            title: "Test Care Plan",
            description: "Test care plan description"
        )
        try carePlan.save(on: db).wait()
        
        let dueDate = Date().addingTimeInterval(Double(dueInHours * 3600))
        let goal = Goal(
            title: "Test Goal",
            description: "Test goal description",
            targetDate: dueDate,
            carePlanID: try carePlan.requireID()
        )
        try goal.save(on: db).wait()
        return goal
    }
    
    // MARK: - Notification Creation
    static func createTestNotification(for user: User, on db: Database, read: Bool = false) throws -> UserNotification {
        let notification = UserNotification(
            title: "Test Notification",
            kind: "reminder",
            message: "This is a test notification",
            read: read,
            userID: try user.requireID(),
            entityType: "task",
            entityID: UUID(),
            deliveryChannel: "in_app",
            deliveryStatus: "sent"
        )
        try notification.save(on: db).wait()
        return notification
    }
    
    // MARK: - Template Creation
    static func createTestTemplate(on db: Database) throws -> NotificationTemplate {
        let template = NotificationTemplate(
            templateKey: "test_template",
            entityType: "task",
            deliveryChannel: "email",
            subjectTemplate: "Test: {entity_title}",
            bodyTemplate: "Hello {user_name}, your {entity_type} '{entity_title}' is due {time_until_due}.",
            variables: ["entity_title", "user_name", "entity_type", "time_until_due"]
        )
        try template.save(on: db).wait()
        return template
    }
    
    // MARK: - Schedule Creation
    static func createTestSchedule(for user: User, on db: Database, dueInHours: Int = 24, reminderHours: Int = 24) throws -> NotificationSchedule {
        let dueDate = Date().addingTimeInterval(Double(dueInHours * 3600))
        let scheduledTime = dueDate.addingTimeInterval(-Double(reminderHours * 3600))
        
        let schedule = NotificationSchedule(
            userID: try user.requireID(),
            entityType: "task",
            entityID: UUID(),
            entityTitle: "Test Task",
            dueDate: dueDate,
            reminderHoursBefore: reminderHours,
            scheduledTime: scheduledTime
        )
        try schedule.save(on: db).wait()
        return schedule
    }
    
    // MARK: - Cleanup
    static func cleanup(app: Application) throws {
        // Clean up test data
        try UserNotification.query(on: app.db).delete().wait()
        try NotificationSchedule.query(on: app.db).delete().wait()
        try UserNotificationPreferences.query(on: app.db).delete().wait()
        try NotificationTemplate.query(on: app.db).delete().wait()
        try TaskModel.query(on: app.db).delete().wait()
        try Goal.query(on: app.db).delete().wait()
        try CarePlan.query(on: app.db).delete().wait()
        try User.query(on: app.db).delete().wait()
    }
}

// MARK: - Mock Services for Testing
class MockNotificationDeliveryService: NotificationDeliveryService {
    var deliveryResults: [DeliveryResult] = []
    var shouldFail = false
    
    override func deliverNotification(_ payload: NotificationPayload, via channels: [DeliveryChannel], on req: Request) -> EventLoopFuture<[DeliveryResult]> {
        if shouldFail {
            let failedResults = channels.map { channel in
                DeliveryResult(
                    channel: channel,
                    success: false,
                    messageId: nil,
                    error: "Mock delivery failure",
                    responseData: nil
                )
            }
            return req.eventLoop.makeSucceededFuture(failedResults)
        }
        
        let successResults = channels.map { channel in
            DeliveryResult(
                channel: channel,
                success: true,
                messageId: "mock-message-id",
                error: nil,
                responseData: ["mock": "data"]
            )
        }
        
        deliveryResults.append(contentsOf: successResults)
        return req.eventLoop.makeSucceededFuture(successResults)
    }
}

class MockNotificationSchedulerService: NotificationSchedulerService {
    var scheduledNotifications: [NotifiableEntity] = []
    var cancelledEntities: [(EntityType, UUID)] = []
    
    override func scheduleNotifications<T: NotifiableEntity>(for entity: T, on req: Request) -> EventLoopFuture<Void> {
        scheduledNotifications.append(entity)
        return req.eventLoop.makeSucceededFuture(())
    }
    
    override func cancelSchedules(for entityType: EntityType, entityID: UUID, on req: Request) -> EventLoopFuture<Void> {
        cancelledEntities.append((entityType, entityID))
        return req.eventLoop.makeSucceededFuture(())
    }
}

// MARK: - Test Extensions
extension XCTestCase {
    
    func createTestApp() throws -> Application {
        let app = try Application.testable()
        try app.autoMigrate().wait()
        return app
    }
    
    func tearDownTestApp(_ app: Application) throws {
        try TestUtilities.cleanup(app: app)
        app.shutdown()
    }
    
    // Helper to create authenticated request
    func authenticatedRequest(for user: User, method: HTTPMethod, path: String, on app: Application) -> XCTHTTPRequest {
        var request = XCTHTTPRequest(method: method, url: URI(string: path), headers: HTTPHeaders(), body: nil)
        request.headers.add(name: "X-User-ID", value: try! user.requireID().uuidString)
        return request
    }
}

// MARK: - Test Data Factories
struct NotificationTestDataFactory {
    
    static func sampleNotificationPayload(userID: UUID = UUID(), entityID: UUID = UUID()) -> NotificationPayload {
        return NotificationPayload(
            title: "Test Notification",
            message: "This is a test notification message",
            entityType: .task,
            entityID: entityID,
            userID: userID,
            dueDate: Date().addingTimeInterval(86400),
            reminderHoursBefore: 24,
            metadata: ["test": "true"]
        )
    }
    
    static func sampleTemplateVariables() -> TemplateVariables {
        return TemplateVariables(
            entityTitle: "Complete project documentation",
            entityType: "task",
            dueDate: Date().addingTimeInterval(86400),
            reminderHours: 24,
            userName: "John Doe",
            userEmail: "<EMAIL>",
            appUrl: "https://app.wellup.com",
            organizationName: "Wellup"
        )
    }
    
    static func samplePreferencesRequest() -> NotificationPreferencesRequest {
        return NotificationPreferencesRequest(
            smsEnabled: true,
            smsPhoneNumber: "+1234567890",
            emailEnabled: true,
            emailAddress: "<EMAIL>",
            pushEnabled: true,
            browserEnabled: true,
            inAppEnabled: true,
            reminderHours: [24, 48],
            quietHoursStart: 22,
            quietHoursEnd: 8,
            timezone: "America/New_York"
        )
    }
    
    static func sampleTemplateRequest() -> NotificationTemplateRequest {
        return NotificationTemplateRequest(
            templateKey: "test_template",
            entityType: "task",
            deliveryChannel: "email",
            subjectTemplate: "Test: {entity_title}",
            bodyTemplate: "Hello {user_name}, test message",
            variables: ["entity_title", "user_name"],
            isActive: true
        )
    }
}

// MARK: - Performance Testing Utilities
struct PerformanceTestUtilities {
    
    static func measureTime<T>(operation: () throws -> T) rethrows -> (result: T, duration: TimeInterval) {
        let startTime = Date()
        let result = try operation()
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        return (result, duration)
    }
    
    static func createBulkNotifications(count: Int, for user: User, on db: Database) throws -> [UserNotification] {
        let notifications = (1...count).map { i in
            UserNotification(
                title: "Bulk Notification \(i)",
                kind: "reminder",
                message: "Bulk test message \(i)",
                read: false,
                userID: try! user.requireID()
            )
        }
        
        let saveFutures = notifications.map { $0.save(on: db) }
        try EventLoopFuture.andAllSucceed(saveFutures, on: db.eventLoop).wait()
        
        return notifications
    }
}
