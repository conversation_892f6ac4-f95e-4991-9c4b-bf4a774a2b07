//
//  NotificationAPITests.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import XCTest
import Vapor
import Fluent
@testable import App

final class NotificationAPITests: XCTestCase {
    var app: Application!
    var testUser: User!
    
    override func setUpWithError() throws {
        app = Application(.testing)
        try configure(app)
        try app.autoMigrate().wait()
        
        // Create test user
        testUser = User(firstName: "Test", lastName: "User", email: "<EMAIL>")
        try testUser.save(on: app.db).wait()
    }
    
    override func tearDownWithError() throws {
        app.shutdown()
    }
    
    // MARK: - Notification Preferences API Tests
    func testCreateNotificationPreferences() throws {
        let requestBody = NotificationPreferencesRequest(
            smsEnabled: true,
            smsPhoneNumber: "+1234567890",
            emailEnabled: true,
            emailAddress: "<EMAIL>",
            pushEnabled: true,
            browserEnabled: true,
            inAppEnabled: true,
            reminderHours: [24, 48],
            quietHoursStart: 22,
            quietHoursEnd: 8,
            timezone: "America/New_York"
        )
        
        try app.test(.POST, "/notification-preferences", beforeRequest: { req in
            req.headers.add(name: "X-User-ID", value: testUser.requireID().uuidString)
            try req.content.encode(requestBody)
        }, afterResponse: { res in
            XCTAssertEqual(res.status, .ok)
            
            let response = try res.content.decode(NotificationPreferencesResponse.self)
            XCTAssertEqual(response.smsEnabled, true)
            XCTAssertEqual(response.smsPhoneNumber, "+1234567890")
            XCTAssertEqual(response.reminderHours, [24, 48])
            XCTAssertEqual(response.timezone, "America/New_York")
        })
    }
    
    func testGetNotificationPreferences() throws {
        // First create preferences
        let preferences = UserNotificationPreferences(
            userID: try testUser.requireID(),
            smsEnabled: true,
            emailEnabled: true,
            reminderHours: [24, 48]
        )
        try preferences.save(on: app.db).wait()
        
        try app.test(.GET, "/notification-preferences", beforeRequest: { req in
            req.headers.add(name: "X-User-ID", value: testUser.requireID().uuidString)
        }, afterResponse: { res in
            XCTAssertEqual(res.status, .ok)
            
            let response = try res.content.decode(NotificationPreferencesResponse.self)
            XCTAssertEqual(response.smsEnabled, true)
            XCTAssertEqual(response.emailEnabled, true)
            XCTAssertEqual(response.reminderHours, [24, 48])
        })
    }
    
    func testTestNotifications() throws {
        // Create preferences first
        let preferences = UserNotificationPreferences(
            userID: try testUser.requireID(),
            smsEnabled: true,
            emailEnabled: true,
            inAppEnabled: true,
            reminderHours: [24]
        )
        try preferences.save(on: app.db).wait()
        
        let testRequest = NotificationTestRequest(
            channels: ["in_app", "email"],
            message: "This is a test notification"
        )
        
        try app.test(.POST, "/notification-preferences/test", beforeRequest: { req in
            req.headers.add(name: "X-User-ID", value: testUser.requireID().uuidString)
            try req.content.encode(testRequest)
        }, afterResponse: { res in
            XCTAssertEqual(res.status, .accepted)
        })
    }
    
    // MARK: - Notification Template API Tests
    func testListNotificationTemplates() throws {
        // Initialize default templates
        try app.notificationTemplateService.initializeDefaultTemplates(on: app.db).wait()
        
        try app.test(.GET, "/notification-templates", afterResponse: { res in
            XCTAssertEqual(res.status, .ok)
            
            let templates = try res.content.decode([NotificationTemplateResponse].self)
            XCTAssertFalse(templates.isEmpty)
            
            // Check for specific templates
            let taskSMSTemplate = templates.first { $0.templateKey == "task_reminder_sms" }
            XCTAssertNotNil(taskSMSTemplate)
        })
    }
    
    func testCreateNotificationTemplate() throws {
        let templateRequest = NotificationTemplateRequest(
            templateKey: "custom_test_template",
            entityType: "task",
            deliveryChannel: "email",
            subjectTemplate: "Test: {entity_title}",
            bodyTemplate: "Hello {user_name}, test message for {entity_title}",
            variables: ["entity_title", "user_name"],
            isActive: true
        )
        
        try app.test(.POST, "/notification-templates", beforeRequest: { req in
            try req.content.encode(templateRequest)
        }, afterResponse: { res in
            XCTAssertEqual(res.status, .ok)
            
            let response = try res.content.decode(NotificationTemplateResponse.self)
            XCTAssertEqual(response.templateKey, "custom_test_template")
            XCTAssertEqual(response.entityType, "task")
            XCTAssertEqual(response.deliveryChannel, "email")
            XCTAssertTrue(response.isActive)
        })
    }
    
    func testPreviewNotificationTemplate() throws {
        let previewRequest = TemplatePreviewRequest(
            templateId: nil,
            templateKey: "test_preview",
            entityType: "task",
            deliveryChannel: "email",
            subjectTemplate: "Task: {entity_title}",
            bodyTemplate: "Hello {user_name}, your {entity_type} '{entity_title}' is due {time_until_due}.",
            variables: ["entity_title", "user_name", "entity_type", "time_until_due"],
            sampleData: TemplatePreviewData(
                entityTitle: "Complete project",
                dueDate: ISO8601DateFormatter().string(from: Date().addingTimeInterval(86400)),
                reminderHours: 24,
                userName: "John",
                userEmail: "<EMAIL>"
            )
        )
        
        try app.test(.POST, "/notification-templates/preview", beforeRequest: { req in
            try req.content.encode(previewRequest)
        }, afterResponse: { res in
            XCTAssertEqual(res.status, .ok)
            
            let response = try res.content.decode(TemplatePreviewResponse.self)
            XCTAssertEqual(response.subject, "Task: Complete project")
            XCTAssertTrue(response.body.contains("Hello John"))
            XCTAssertTrue(response.body.contains("Complete project"))
            XCTAssertTrue(response.body.contains("tomorrow"))
        })
    }
    
    // MARK: - Notification History API Tests
    func testGetNotificationHistory() throws {
        // Create test notifications
        let notifications = [
            UserNotification(
                title: "Test Notification 1",
                kind: "reminder",
                message: "Test message 1",
                read: false,
                userID: try testUser.requireID()
            ),
            UserNotification(
                title: "Test Notification 2",
                kind: "standard",
                message: "Test message 2",
                read: true,
                userID: try testUser.requireID()
            )
        ]
        
        let saveFutures = notifications.map { $0.save(on: app.db) }
        try EventLoopFuture.andAllSucceed(saveFutures, on: app.eventLoopGroup.next()).wait()
        
        try app.test(.GET, "/notifications", beforeRequest: { req in
            req.headers.add(name: "X-User-ID", value: testUser.requireID().uuidString)
        }, afterResponse: { res in
            XCTAssertEqual(res.status, .ok)
            
            let page = try res.content.decode(Page<NotificationHistoryResponse>.self)
            XCTAssertEqual(page.items.count, 2)
            XCTAssertTrue(page.items.contains { $0.title == "Test Notification 1" })
            XCTAssertTrue(page.items.contains { $0.title == "Test Notification 2" })
        })
    }
    
    func testGetNotificationStats() throws {
        // Create test notifications with different statuses
        let notifications = [
            UserNotification(title: "Read 1", kind: "reminder", message: "Message", read: true, userID: try testUser.requireID()),
            UserNotification(title: "Read 2", kind: "standard", message: "Message", read: true, userID: try testUser.requireID()),
            UserNotification(title: "Unread 1", kind: "reminder", message: "Message", read: false, userID: try testUser.requireID()),
            UserNotification(title: "Unread 2", kind: "urgent", message: "Message", read: false, userID: try testUser.requireID())
        ]
        
        let saveFutures = notifications.map { $0.save(on: app.db) }
        try EventLoopFuture.andAllSucceed(saveFutures, on: app.eventLoopGroup.next()).wait()
        
        try app.test(.GET, "/notifications/stats", beforeRequest: { req in
            req.headers.add(name: "X-User-ID", value: testUser.requireID().uuidString)
        }, afterResponse: { res in
            XCTAssertEqual(res.status, .ok)
            
            let stats = try res.content.decode(NotificationStatsResponse.self)
            XCTAssertEqual(stats.totalNotifications, 4)
            XCTAssertEqual(stats.readNotifications, 2)
            XCTAssertEqual(stats.unreadNotifications, 2)
            XCTAssertEqual(stats.notificationsByKind["reminder"], 2)
            XCTAssertEqual(stats.notificationsByKind["standard"], 1)
            XCTAssertEqual(stats.notificationsByKind["urgent"], 1)
        })
    }
    
    func testMarkNotificationAsRead() throws {
        let notification = UserNotification(
            title: "Test Notification",
            kind: "reminder",
            message: "Test message",
            read: false,
            userID: try testUser.requireID()
        )
        try notification.save(on: app.db).wait()
        
        try app.test(.PUT, "/notifications/\(notification.requireID())/read", beforeRequest: { req in
            req.headers.add(name: "X-User-ID", value: testUser.requireID().uuidString)
        }, afterResponse: { res in
            XCTAssertEqual(res.status, .noContent)
        })
        
        // Verify notification was marked as read
        let updatedNotification = try UserNotification.find(notification.id, on: app.db).wait()
        XCTAssertTrue(updatedNotification?.read ?? false)
    }
    
    func testBulkMarkAsRead() throws {
        let notifications = [
            UserNotification(title: "Test 1", kind: "reminder", message: "Message", read: false, userID: try testUser.requireID()),
            UserNotification(title: "Test 2", kind: "reminder", message: "Message", read: false, userID: try testUser.requireID())
        ]
        
        let saveFutures = notifications.map { $0.save(on: app.db) }
        try EventLoopFuture.andAllSucceed(saveFutures, on: app.eventLoopGroup.next()).wait()
        
        let markReadRequest = MarkReadRequest(
            notificationIds: notifications.compactMap { $0.id },
            markAll: false
        )
        
        try app.test(.POST, "/notifications/mark-read", beforeRequest: { req in
            req.headers.add(name: "X-User-ID", value: testUser.requireID().uuidString)
            try req.content.encode(markReadRequest)
        }, afterResponse: { res in
            XCTAssertEqual(res.status, .ok)
            
            let response = try res.content.decode([String: Int].self)
            XCTAssertEqual(response["marked_as_read"], 2)
        })
    }
    
    // MARK: - Health Check API Tests
    func testBasicHealthCheck() throws {
        try app.test(.GET, "/health", afterResponse: { res in
            XCTAssertEqual(res.status, .ok)
            
            let health = try res.content.decode(HealthCheckResponse.self)
            XCTAssertTrue(["healthy", "degraded", "unhealthy"].contains(health.status))
            XCTAssertFalse(health.components.isEmpty)
            XCTAssertGreaterThan(health.overall.totalComponents, 0)
        })
    }
    
    func testDetailedHealthCheck() throws {
        try app.test(.GET, "/health/detailed", afterResponse: { res in
            XCTAssertEqual(res.status, .ok)
            
            let health = try res.content.decode(HealthCheckResponse.self)
            XCTAssertFalse(health.components.isEmpty)
            
            // Should include database component
            let dbComponent = health.components.first { $0.name == "database" }
            XCTAssertNotNil(dbComponent)
            XCTAssertEqual(dbComponent?.status, "healthy")
        })
    }
    
    func testComponentHealthCheck() throws {
        try app.test(.GET, "/health/components/database", afterResponse: { res in
            XCTAssertEqual(res.status, .ok)
            
            let component = try res.content.decode(ComponentHealth.self)
            XCTAssertEqual(component.name, "database")
            XCTAssertEqual(component.status, "healthy")
            XCTAssertNotNil(component.responseTime)
        })
    }
    
    // MARK: - Error Handling Tests
    func testInvalidNotificationPreferences() throws {
        let invalidRequest = NotificationPreferencesRequest(
            smsEnabled: true,
            smsPhoneNumber: "invalid-phone", // Invalid phone format
            emailEnabled: true,
            emailAddress: "invalid-email", // Invalid email format
            pushEnabled: true,
            browserEnabled: true,
            inAppEnabled: true,
            reminderHours: [], // Invalid: empty array
            quietHoursStart: nil,
            quietHoursEnd: nil,
            timezone: nil
        )
        
        try app.test(.POST, "/notification-preferences", beforeRequest: { req in
            req.headers.add(name: "X-User-ID", value: testUser.requireID().uuidString)
            try req.content.encode(invalidRequest)
        }, afterResponse: { res in
            // Should still succeed as validation is not strict in this implementation
            // In a production system, you might want stricter validation
            XCTAssertEqual(res.status, .ok)
        })
    }
    
    func testUnauthorizedAccess() throws {
        try app.test(.GET, "/notification-preferences", afterResponse: { res in
            XCTAssertEqual(res.status, .unauthorized)
        })
    }
    
    func testNotFoundNotification() throws {
        let nonExistentID = UUID()
        
        try app.test(.GET, "/notifications/\(nonExistentID)", beforeRequest: { req in
            req.headers.add(name: "X-User-ID", value: testUser.requireID().uuidString)
        }, afterResponse: { res in
            XCTAssertEqual(res.status, .notFound)
        })
    }
}
