//
//  NotificationServiceTests.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import XCTest
import Vapor
import Fluent
@testable import App

final class NotificationServiceTests: XCTestCase {
    var app: Application!
    
    override func setUpWithError() throws {
        app = Application(.testing)
        try configure(app)
        try app.autoMigrate().wait()
    }
    
    override func tearDownWithError() throws {
        app.shutdown()
    }
    
    // MARK: - Notification Preferences Tests
    func testCreateNotificationPreferences() throws {
        let user = User(firstName: "Test", lastName: "User", email: "<EMAIL>")
        try user.save(on: app.db).wait()
        
        let preferences = UserNotificationPreferences(
            userID: try user.requireID(),
            smsEnabled: true,
            smsPhoneNumber: "+1234567890",
            emailEnabled: true,
            emailAddress: "<EMAIL>",
            reminderHours: [24, 48]
        )
        
        XCTAssertNoThrow(try preferences.save(on: app.db).wait())
        
        let savedPreferences = try UserNotificationPreferences.query(on: app.db)
            .filter(\.$user.$id == user.requireID())
            .first()
            .wait()
        
        XCTAssertNotNil(savedPreferences)
        XCTAssertEqual(savedPreferences?.smsEnabled, true)
        XCTAssertEqual(savedPreferences?.smsPhoneNumber, "+1234567890")
        XCTAssertEqual(savedPreferences?.reminderHours, [24, 48])
    }
    
    func testNotificationScheduleCreation() throws {
        let user = User(firstName: "Test", lastName: "User", email: "<EMAIL>")
        try user.save(on: app.db).wait()
        
        let dueDate = Date().addingTimeInterval(86400) // 24 hours from now
        let scheduledTime = dueDate.addingTimeInterval(-24 * 3600) // 24 hours before due
        
        let schedule = NotificationSchedule(
            userID: try user.requireID(),
            entityType: "task",
            entityID: UUID(),
            entityTitle: "Test Task",
            dueDate: dueDate,
            reminderHoursBefore: 24,
            scheduledTime: scheduledTime
        )
        
        XCTAssertNoThrow(try schedule.save(on: app.db).wait())
        
        let savedSchedule = try NotificationSchedule.find(schedule.id, on: app.db).wait()
        XCTAssertNotNil(savedSchedule)
        XCTAssertEqual(savedSchedule?.entityType, "task")
        XCTAssertEqual(savedSchedule?.reminderHoursBefore, 24)
        XCTAssertEqual(savedSchedule?.status, "scheduled")
    }
    
    func testNotificationTemplateRendering() throws {
        let template = NotificationTemplate(
            templateKey: "test_template",
            entityType: "task",
            deliveryChannel: "email",
            subjectTemplate: "Task Reminder: {entity_title}",
            bodyTemplate: "Hello {user_name}, your {entity_type} '{entity_title}' is due {time_until_due}.",
            variables: ["entity_title", "user_name", "entity_type", "time_until_due"]
        )
        
        let variables = TemplateVariables(
            entityTitle: "Complete project",
            entityType: "task",
            dueDate: Date().addingTimeInterval(86400),
            reminderHours: 24,
            userName: "John",
            userEmail: "<EMAIL>",
            appUrl: "https://app.wellup.com",
            organizationName: "Wellup"
        )
        
        let rendered = NotificationTemplateRenderer.render(template: template, variables: variables)
        
        XCTAssertEqual(rendered.subject, "Task Reminder: Complete project")
        XCTAssertTrue(rendered.body.contains("Hello John"))
        XCTAssertTrue(rendered.body.contains("your task 'Complete project'"))
        XCTAssertTrue(rendered.body.contains("is due tomorrow"))
    }
    
    func testTemplateVariableExtraction() throws {
        let service = app.notificationTemplateService
        
        let template = NotificationTemplate(
            templateKey: "test_template",
            entityType: "task",
            deliveryChannel: "email",
            bodyTemplate: "Hello {user_name}, your {entity_type} '{entity_title}' is due {time_until_due}.",
            variables: ["user_name", "entity_type", "entity_title"] // Missing time_until_due
        )
        
        let errors = service.validateTemplate(template)
        XCTAssertFalse(errors.isEmpty)
        XCTAssertTrue(errors.contains { $0.contains("time_until_due") })
    }
    
    // MARK: - Notification Delivery Tests
    func testInAppNotificationCreation() throws {
        let user = User(firstName: "Test", lastName: "User", email: "<EMAIL>")
        try user.save(on: app.db).wait()
        
        let notification = UserNotification(
            title: "Test Notification",
            kind: "reminder",
            message: "This is a test notification",
            read: false,
            userID: try user.requireID(),
            entityType: "task",
            entityID: UUID(),
            deliveryChannel: "in_app",
            deliveryStatus: "sent"
        )
        
        XCTAssertNoThrow(try notification.save(on: app.db).wait())
        
        let savedNotification = try UserNotification.find(notification.id, on: app.db).wait()
        XCTAssertNotNil(savedNotification)
        XCTAssertEqual(savedNotification?.title, "Test Notification")
        XCTAssertEqual(savedNotification?.read, false)
        XCTAssertEqual(savedNotification?.deliveryChannel, "in_app")
    }
    
    func testNotificationPayloadCreation() throws {
        let payload = NotificationPayload(
            title: "Task Reminder",
            message: "Your task is due tomorrow",
            entityType: .task,
            entityID: UUID(),
            userID: UUID(),
            dueDate: Date().addingTimeInterval(86400),
            reminderHoursBefore: 24,
            metadata: ["test": "true"]
        )
        
        XCTAssertEqual(payload.title, "Task Reminder")
        XCTAssertEqual(payload.entityType, .task)
        XCTAssertEqual(payload.reminderHoursBefore, 24)
        XCTAssertEqual(payload.metadata?["test"], "true")
    }
    
    // MARK: - Template Service Tests
    func testDefaultTemplateInitialization() throws {
        let service = app.notificationTemplateService
        
        try service.initializeDefaultTemplates(on: app.db).wait()
        
        let templates = try NotificationTemplate.query(on: app.db).all().wait()
        XCTAssertFalse(templates.isEmpty)
        
        // Check for specific default templates
        let taskSMSTemplate = templates.first { $0.templateKey == "task_reminder_sms" }
        XCTAssertNotNil(taskSMSTemplate)
        XCTAssertEqual(taskSMSTemplate?.entityType, "task")
        XCTAssertEqual(taskSMSTemplate?.deliveryChannel, "sms")
        
        let goalEmailTemplate = templates.first { $0.templateKey == "goal_reminder_email" }
        XCTAssertNotNil(goalEmailTemplate)
        XCTAssertEqual(goalEmailTemplate?.entityType, "goal")
        XCTAssertEqual(goalEmailTemplate?.deliveryChannel, "email")
    }
    
    func testTemplateRetrieval() throws {
        let service = app.notificationTemplateService
        
        // Initialize default templates
        try service.initializeDefaultTemplates(on: app.db).wait()
        
        // Test template retrieval
        let template = try service.getTemplate(
            for: .task,
            deliveryChannel: .sms,
            on: app.db
        ).wait()
        
        XCTAssertNotNil(template)
        XCTAssertEqual(template?.entityType, "task")
        XCTAssertEqual(template?.deliveryChannel, "sms")
    }
    
    func testCustomTemplateCreation() throws {
        let service = app.notificationTemplateService
        
        let template = try service.createTemplate(
            templateKey: "custom_task_email",
            entityType: "task",
            deliveryChannel: "email",
            subjectTemplate: "Custom Task: {entity_title}",
            bodyTemplate: "Custom message for {user_name}",
            variables: ["entity_title", "user_name"],
            on: app.db
        ).wait()
        
        XCTAssertEqual(template.templateKey, "custom_task_email")
        XCTAssertEqual(template.entityType, "task")
        XCTAssertEqual(template.deliveryChannel, "email")
        XCTAssertTrue(template.isActive)
    }
    
    // MARK: - Entity Extension Tests
    func testTaskNotifiableEntity() throws {
        let user = User(firstName: "Test", lastName: "User", email: "<EMAIL>")
        try user.save(on: app.db).wait()
        
        let task = TaskModel(
            title: "Test Task",
            description: "Test Description",
            dueAtEpoc: Int(Date().addingTimeInterval(86400).timeIntervalSince1970),
            creatorID: try user.requireID(),
            assigneeID: try user.requireID()
        )
        
        XCTAssertEqual(task.entityType, .task)
        XCTAssertEqual(task.title, "Test Task")
        XCTAssertFalse(task.assignedUserIDs.isEmpty)
        XCTAssertTrue(task.assignedUserIDs.contains(try user.requireID()))
    }
    
    // MARK: - Error Handling Tests
    func testInvalidTemplateValidation() throws {
        let service = app.notificationTemplateService
        
        let invalidTemplate = NotificationTemplate(
            templateKey: "", // Invalid: empty key
            entityType: "invalid_type", // Invalid: not a valid entity type
            deliveryChannel: "invalid_channel", // Invalid: not a valid channel
            bodyTemplate: "", // Invalid: empty body
            variables: []
        )
        
        let errors = service.validateTemplate(invalidTemplate)
        XCTAssertFalse(errors.isEmpty)
        XCTAssertTrue(errors.contains { $0.contains("Template key is required") })
        XCTAssertTrue(errors.contains { $0.contains("Invalid entity type") })
        XCTAssertTrue(errors.contains { $0.contains("Invalid delivery channel") })
        XCTAssertTrue(errors.contains { $0.contains("Body template is required") })
    }
    
    func testScheduleForPastDate() throws {
        let user = User(firstName: "Test", lastName: "User", email: "<EMAIL>")
        try user.save(on: app.db).wait()
        
        let pastDate = Date().addingTimeInterval(-86400) // 24 hours ago
        let scheduledTime = pastDate.addingTimeInterval(-24 * 3600) // 24 hours before past due date
        
        let schedule = NotificationSchedule(
            userID: try user.requireID(),
            entityType: "task",
            entityID: UUID(),
            entityTitle: "Past Task",
            dueDate: pastDate,
            reminderHoursBefore: 24,
            scheduledTime: scheduledTime
        )
        
        // Should save but not create EventBridge schedule for past dates
        XCTAssertNoThrow(try schedule.save(on: app.db).wait())
        XCTAssertNil(schedule.eventBridgeScheduleName)
    }
    
    // MARK: - Performance Tests
    func testBulkNotificationCreation() throws {
        let user = User(firstName: "Test", lastName: "User", email: "<EMAIL>")
        try user.save(on: app.db).wait()
        
        let notifications = (1...100).map { i in
            UserNotification(
                title: "Notification \(i)",
                kind: "reminder",
                message: "Test message \(i)",
                read: false,
                userID: try! user.requireID(),
                entityType: "task",
                entityID: UUID()
            )
        }
        
        let startTime = Date()
        
        let saveFutures = notifications.map { notification in
            notification.save(on: app.db)
        }
        
        XCTAssertNoThrow(try EventLoopFuture.andAllSucceed(saveFutures, on: app.eventLoopGroup.next()).wait())
        
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        
        // Should complete within reasonable time (adjust threshold as needed)
        XCTAssertLessThan(duration, 5.0, "Bulk notification creation took too long: \(duration)s")
        
        let savedCount = try UserNotification.query(on: app.db)
            .filter(\.$userID == user.requireID())
            .count()
            .wait()
        
        XCTAssertEqual(savedCount, 100)
    }
}
