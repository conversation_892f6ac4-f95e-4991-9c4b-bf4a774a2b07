# CloudWatch Alarms for Wellup Notification Service
# Deploy using AWS CLI or CloudFormation

AWSTemplateFormatVersion: '2010-09-09'
Description: 'CloudWatch Alarms for Wellup Notification Service'

Parameters:
  Environment:
    Type: String
    Default: staging
    AllowedValues: [staging, production]
    Description: Environment name
  
  SNSTopicArn:
    Type: String
    Description: SNS Topic ARN for alarm notifications

Resources:
  # High Notification Failure Rate
  NotificationFailureRateAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-failure-rate-${Environment}'
      AlarmDescription: 'High notification failure rate detected'
      MetricName: NotificationDelivered
      Namespace: Wellup/Notifications
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 10
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: Status
          Value: Failed
      AlarmActions:
        - !Ref SNSTopicArn
      TreatMissingData: notBreaching

  # High API Latency
  APILatencyAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-api-latency-${Environment}'
      AlarmDescription: 'High API latency detected'
      MetricName: APILatency
      Namespace: Wellup/Notifications
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 5000
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - !Ref SNSTopicArn
      TreatMissingData: notBreaching

  # Large Retry Queue Size
  RetryQueueSizeAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-retry-queue-size-${Environment}'
      AlarmDescription: 'Large retry queue size detected'
      MetricName: RetryQueueSize
      Namespace: Wellup/Notifications
      Statistic: Average
      Period: 300
      EvaluationPeriods: 1
      Threshold: 100
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - !Ref SNSTopicArn
      TreatMissingData: notBreaching

  # Dead Letter Queue Messages
  DLQMessagesAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-dlq-messages-${Environment}'
      AlarmDescription: 'Messages in Dead Letter Queue'
      MetricName: DeadLetterQueueSize
      Namespace: Wellup/Notifications
      Statistic: Average
      Period: 300
      EvaluationPeriods: 1
      Threshold: 1
      ComparisonOperator: GreaterThanOrEqualToThreshold
      AlarmActions:
        - !Ref SNSTopicArn
      TreatMissingData: notBreaching

  # Lambda Function Errors
  LambdaErrorsAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-lambda-errors-${Environment}'
      AlarmDescription: 'Lambda function errors detected'
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 5
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Sub 'notification-processor-${Environment}'
      AlarmActions:
        - !Ref SNSTopicArn
      TreatMissingData: notBreaching

  # Lambda Function Duration
  LambdaDurationAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-lambda-duration-${Environment}'
      AlarmDescription: 'Lambda function duration too high'
      MetricName: Duration
      Namespace: AWS/Lambda
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 30000
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Sub 'notification-processor-${Environment}'
      AlarmActions:
        - !Ref SNSTopicArn
      TreatMissingData: notBreaching

  # Health Check Failures
  HealthCheckFailureAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-health-check-failure-${Environment}'
      AlarmDescription: 'Health check failures detected'
      MetricName: HealthCheck
      Namespace: Wellup/Notifications
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 0.5
      ComparisonOperator: LessThanThreshold
      AlarmActions:
        - !Ref SNSTopicArn
      TreatMissingData: breaching

  # SMS Delivery Failures
  SMSFailureAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-sms-failures-${Environment}'
      AlarmDescription: 'High SMS delivery failure rate'
      MetricName: NotificationErrors
      Namespace: Wellup/Notifications
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 20
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: Channel
          Value: sms
      AlarmActions:
        - !Ref SNSTopicArn
      TreatMissingData: notBreaching

  # Email Delivery Failures
  EmailFailureAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-email-failures-${Environment}'
      AlarmDescription: 'High email delivery failure rate'
      MetricName: NotificationErrors
      Namespace: Wellup/Notifications
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 20
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: Channel
          Value: email
      AlarmActions:
        - !Ref SNSTopicArn
      TreatMissingData: notBreaching

  # Schedule Processing Failures
  ScheduleProcessingFailureAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-schedule-processing-failures-${Environment}'
      AlarmDescription: 'High schedule processing failure rate'
      MetricName: ScheduleProcessed
      Namespace: Wellup/Notifications
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 10
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: Status
          Value: Failed
      AlarmActions:
        - !Ref SNSTopicArn
      TreatMissingData: notBreaching

  # Composite Alarm for Overall Service Health
  NotificationServiceHealthAlarm:
    Type: AWS::CloudWatch::CompositeAlarm
    Properties:
      AlarmName: !Sub 'notification-service-health-${Environment}'
      AlarmDescription: 'Overall notification service health'
      AlarmRule: !Sub |
        ALARM(${NotificationFailureRateAlarm}) OR
        ALARM(${APILatencyAlarm}) OR
        ALARM(${RetryQueueSizeAlarm}) OR
        ALARM(${DLQMessagesAlarm}) OR
        ALARM(${LambdaErrorsAlarm}) OR
        ALARM(${HealthCheckFailureAlarm})
      AlarmActions:
        - !Ref SNSTopicArn

Outputs:
  NotificationFailureRateAlarmArn:
    Description: 'Notification Failure Rate Alarm ARN'
    Value: !GetAtt NotificationFailureRateAlarm.Arn
    Export:
      Name: !Sub '${AWS::StackName}-NotificationFailureRateAlarmArn'

  NotificationServiceHealthAlarmArn:
    Description: 'Notification Service Health Composite Alarm ARN'
    Value: !GetAtt NotificationServiceHealthAlarm.Arn
    Export:
      Name: !Sub '${AWS::StackName}-NotificationServiceHealthAlarmArn'
