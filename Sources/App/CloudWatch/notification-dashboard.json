{"widgets": [{"type": "metric", "x": 0, "y": 0, "width": 12, "height": 6, "properties": {"metrics": [["Wellup/Notifications", "NotificationDelivered", "Status", "Success"], ["...", "Failed"]], "view": "timeSeries", "stacked": false, "region": "us-east-1", "title": "Notification Delivery Success/Failure", "period": 300, "stat": "Sum"}}, {"type": "metric", "x": 12, "y": 0, "width": 12, "height": 6, "properties": {"metrics": [["Wellup/Notifications", "NotificationsByChannel", "Channel", "sms"], ["...", "email"], ["...", "push"], ["...", "browser"], ["...", "in_app"]], "view": "timeSeries", "stacked": false, "region": "us-east-1", "title": "Notifications by Channel", "period": 300, "stat": "Sum"}}, {"type": "metric", "x": 0, "y": 6, "width": 8, "height": 6, "properties": {"metrics": [["Wellup/Notifications", "NotificationScheduled", "EntityType", "task"], ["...", "goal"], ["...", "intervention"], ["...", "follow_up"]], "view": "timeSeries", "stacked": false, "region": "us-east-1", "title": "Notifications Scheduled by Entity Type", "period": 300, "stat": "Sum"}}, {"type": "metric", "x": 8, "y": 6, "width": 8, "height": 6, "properties": {"metrics": [["Wellup/Notifications", "ScheduleProcessingTime", "EntityType", "task"], ["...", "goal"], ["...", "intervention"], ["...", "follow_up"]], "view": "timeSeries", "stacked": false, "region": "us-east-1", "title": "Schedule Processing Time (ms)", "period": 300, "stat": "Average"}}, {"type": "metric", "x": 16, "y": 6, "width": 8, "height": 6, "properties": {"metrics": [["Wellup/Notifications", "RetryQueueSize"], [".", "DeadLetterQueueSize"]], "view": "timeSeries", "stacked": false, "region": "us-east-1", "title": "Queue Sizes", "period": 300, "stat": "Average"}}, {"type": "metric", "x": 0, "y": 12, "width": 12, "height": 6, "properties": {"metrics": [["Wellup/Notifications", "NotificationErrors", "Channel", "sms"], ["...", "email"], ["...", "push"], ["...", "browser"]], "view": "timeSeries", "stacked": false, "region": "us-east-1", "title": "Notification Errors by Channel", "period": 300, "stat": "Sum"}}, {"type": "metric", "x": 12, "y": 12, "width": 12, "height": 6, "properties": {"metrics": [["Wellup/Notifications", "APILatency", "Endpoint", "/notification-preferences"], ["...", "/notification-templates"], ["...", "/notification-retry"], ["...", "/notifications"]], "view": "timeSeries", "stacked": false, "region": "us-east-1", "title": "API Latency by Endpoint (ms)", "period": 300, "stat": "Average"}}, {"type": "metric", "x": 0, "y": 18, "width": 8, "height": 6, "properties": {"metrics": [["Wellup/Notifications", "TemplateUsage", "Channel", "sms"], ["...", "email"], ["...", "push"], ["...", "browser"], ["...", "in_app"]], "view": "timeSeries", "stacked": false, "region": "us-east-1", "title": "Template Usage by Channel", "period": 300, "stat": "Sum"}}, {"type": "metric", "x": 8, "y": 18, "width": 8, "height": 6, "properties": {"metrics": [["Wellup/Notifications", "HealthCheck", "Component", "database"], ["...", "sqs"], ["...", "eventbridge"], ["...", "twi<PERSON>"], ["...", "sendgrid"]], "view": "timeSeries", "stacked": false, "region": "us-east-1", "title": "Health Check Status", "period": 300, "stat": "Average"}}, {"type": "metric", "x": 16, "y": 18, "width": 8, "height": 6, "properties": {"metrics": [["AWS/Lambda", "Duration", "FunctionName", "notification-processor-staging"], ["...", "notification-retry-staging"]], "view": "timeSeries", "stacked": false, "region": "us-east-1", "title": "Lambda Function Duration", "period": 300, "stat": "Average"}}, {"type": "log", "x": 0, "y": 24, "width": 24, "height": 6, "properties": {"query": "SOURCE '/aws/lambda/notification-processor-staging' | SOURCE '/aws/lambda/notification-retry-staging'\n| fields @timestamp, @message\n| filter @message like /ERROR/\n| sort @timestamp desc\n| limit 100", "region": "us-east-1", "title": "Recent Notification Errors", "view": "table"}}]}