# Wellup Notification Service

A comprehensive, multi-channel notification system for the Wellup healthcare platform that provides automated reminders for Tasks, Goals, Interventions, and Follow-ups.

## 🚀 Features

- **Multi-Channel Delivery**: SMS, Email, Push, Browser, and In-App notifications
- **Automated Scheduling**: 24h and 48h advance reminders using AWS EventBridge
- **Template System**: Customizable notification templates with variable substitution
- **Retry Mechanism**: SQS-based retry system with exponential backoff and Dead Letter Queue
- **User Preferences**: Granular control over notification channels and timing
- **Monitoring**: Comprehensive CloudWatch metrics and health checks
- **Scalable Architecture**: Serverless AWS Lambda functions for processing

## 📋 Table of Contents

- [Architecture](#architecture)
- [Quick Start](#quick-start)
- [Configuration](#configuration)
- [API Documentation](#api-documentation)
- [Templates](#templates)
- [Monitoring](#monitoring)
- [Testing](#testing)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Entity CRUD   │───▶│ Notification     │───▶│ AWS EventBridge │
│ (Task/Goal/etc) │    │ Scheduler        │    │ Scheduler       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ User Receives   │◀───│ Multi-Channel    │◀───│ Lambda Function │
│ Notification    │    │ Delivery Service │    │ Processor       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ SQS Retry Queue │    │ CloudWatch      │
                       │ + DLQ           │    │ Metrics         │
                       └─────────────────┘    └─────────────────┘
```

### Core Components

1. **Notification Models**: Database models for preferences, schedules, and templates
2. **Scheduler Service**: Manages EventBridge schedules for future notifications
3. **Delivery Service**: Handles multi-channel notification delivery
4. **Template Service**: Manages notification templates and rendering
5. **Retry Service**: SQS-based retry mechanism for failed deliveries
6. **Monitoring Service**: CloudWatch metrics and health checks

## 🚀 Quick Start

### Prerequisites

- Swift 5.9+
- PostgreSQL 13+
- AWS Account with EventBridge, Lambda, SQS, SNS, and Pinpoint
- Twilio Account (for SMS)
- SendGrid Account (for Email)

### Installation

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd wellup-notification-service
   swift package resolve
   ```

2. **Environment Variables**
   ```bash
   # Database
   export DATABASE_HOST=localhost
   export DATABASE_NAME=wellup_notifications
   export DATABASE_USERNAME=postgres
   export DATABASE_PASSWORD=password

   # AWS
   export AWS_ACCESS_KEY_ID=your-access-key
   export AWS_SECRET_ACCESS_KEY=your-secret-key
   export AWS_REGION=us-east-1

   # Twilio
   export TWILIO_ACCOUNT_SID=your-account-sid
   export TWILIO_AUTH_TOKEN=your-auth-token

   # SendGrid
   export SENDGRID_API_KEY=your-api-key

   # Application
   export APP_URL=https://app.wellup.com
   export ORGANIZATION_NAME=Wellup
   ```

3. **Database Migration**
   ```bash
   swift run App migrate
   ```

4. **Deploy Lambda Functions**
   ```bash
   cd Sources/App/Lambda
   chmod +x build-lambda.sh
   ./build-lambda.sh
   cd lambda-dist
   ./deploy.sh staging your-aws-profile
   ```

5. **Start the Application**
   ```bash
   swift run App serve
   ```

## ⚙️ Configuration

### User Notification Preferences

Users can configure their notification preferences via the API:

```json
{
  "smsEnabled": true,
  "smsPhoneNumber": "+**********",
  "emailEnabled": true,
  "emailAddress": "<EMAIL>",
  "pushEnabled": true,
  "browserEnabled": true,
  "inAppEnabled": true,
  "reminderHours": [24, 48],
  "quietHoursStart": 22,
  "quietHoursEnd": 8,
  "timezone": "America/New_York"
}
```

### Default Templates

The system includes default templates for all entity types and delivery channels:

- `task_reminder_sms`
- `task_reminder_email`
- `goal_reminder_email`
- `intervention_reminder_sms`
- `follow_up_reminder_email`

### AWS Configuration

Required AWS services and their configurations:

- **EventBridge Scheduler**: For scheduling future notifications
- **Lambda Functions**: For processing scheduled notifications
- **SQS**: For retry queues and dead letter queues
- **SNS**: For push notifications
- **Pinpoint**: For browser push notifications
- **CloudWatch**: For metrics and monitoring

## 📚 API Documentation

### Endpoints

- `GET /notification-preferences` - Get user preferences
- `POST /notification-preferences` - Create/update preferences
- `POST /notification-preferences/test` - Test notifications
- `GET /notification-templates` - List templates
- `POST /notification-templates` - Create template
- `POST /notification-templates/preview` - Preview template
- `GET /notifications` - Get notification history
- `GET /notifications/stats` - Get notification statistics
- `GET /health` - Health check
- `GET /notification-retry/status` - Queue status

For complete API documentation, see [NotificationServiceAPI.md](NotificationServiceAPI.md).

## 🎨 Templates

### Template Variables

Available variables for notification templates:

| Variable | Description | Example |
|----------|-------------|---------|
| `{entity_title}` | Title of the entity | "Complete project documentation" |
| `{entity_type}` | Type of entity | "task" |
| `{due_date}` | Formatted due date | "July 25, 2025 at 2:00 PM" |
| `{time_until_due}` | Human readable time | "tomorrow" |
| `{user_name}` | User's first name | "John" |
| `{app_url}` | Application URL | "https://app.wellup.com" |

### Custom Templates

Create custom templates via the API:

```json
{
  "templateKey": "custom_task_reminder",
  "entityType": "task",
  "deliveryChannel": "email",
  "subjectTemplate": "Urgent: {entity_title}",
  "bodyTemplate": "Hi {user_name}, your {entity_type} '{entity_title}' is due {time_until_due}!",
  "variables": ["entity_title", "user_name", "entity_type", "time_until_due"]
}
```

## 📊 Monitoring

### CloudWatch Metrics

The service publishes comprehensive metrics to CloudWatch:

- `NotificationDelivered` - Success/failure counts by channel
- `NotificationsByChannel` - Volume by delivery channel
- `NotificationErrors` - Error counts by type
- `ScheduleProcessingTime` - Processing latency
- `RetryQueueSize` - Queue depth monitoring
- `APILatency` - API response times

### Health Checks

Health check endpoints monitor:

- Database connectivity
- SQS queue accessibility
- EventBridge service health
- Twilio API status
- SendGrid API status

### Alarms

Pre-configured CloudWatch alarms for:

- High failure rates
- Queue size thresholds
- API latency spikes
- Lambda function errors
- Dead letter queue messages

## 🧪 Testing

### Running Tests

```bash
# Unit tests
swift test --filter NotificationServiceTests

# Integration tests
swift test --filter NotificationAPITests

# All tests
swift test
```

### Test Coverage

The test suite covers:

- ✅ Notification preferences CRUD
- ✅ Template rendering and validation
- ✅ Schedule creation and management
- ✅ Multi-channel delivery
- ✅ API endpoints
- ✅ Error handling
- ✅ Performance scenarios

## 🚀 Deployment

### Staging Deployment

```bash
# Deploy Lambda functions
cd Sources/App/Lambda/lambda-dist
./deploy.sh staging your-aws-profile

# Deploy CloudWatch alarms
aws cloudformation deploy \
  --template-file ../CloudWatch/notification-alarms.yml \
  --stack-name notification-alarms-staging \
  --parameter-overrides Environment=staging

# Deploy application
docker build -t wellup-notifications .
docker push your-registry/wellup-notifications:staging
```

### Production Deployment

```bash
# Production deployment with additional safeguards
./deploy.sh production your-aws-profile

# Verify health checks
curl https://api.wellup.com/health/detailed
```

## 🔧 Troubleshooting

### Common Issues

1. **Notifications not being sent**
   - Check EventBridge schedules: `aws events list-schedules`
   - Verify Lambda function logs: `aws logs tail /aws/lambda/notification-processor-staging`
   - Check SQS queue metrics

2. **High failure rates**
   - Review CloudWatch alarms
   - Check Dead Letter Queue for failed messages
   - Verify third-party service credentials (Twilio, SendGrid)

3. **Template rendering errors**
   - Validate template syntax
   - Check variable availability
   - Review template validation errors in logs

### Debug Commands

```bash
# Check queue status
curl -H "Authorization: Bearer $TOKEN" \
  https://api.wellup.com/notification-retry/status

# View recent failures
aws logs filter-log-events \
  --log-group-name /aws/lambda/notification-processor-staging \
  --filter-pattern "ERROR"

# Reprocess DLQ messages
curl -X POST -H "Authorization: Bearer $TOKEN" \
  https://api.wellup.com/notification-retry/reprocess \
  -d '{"maxMessages": 10, "reason": "Manual reprocess"}'
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Create an issue in the repository
- Contact the development team
- Check the troubleshooting guide above

---

**Built with ❤️ by the Wellup Team**
