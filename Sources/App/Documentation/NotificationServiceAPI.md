# Wellup Notification Service API Documentation

## Overview

The Wellup Notification Service provides comprehensive multi-channel notification capabilities for Tasks, Goals, Interventions, and Follow-ups. It supports SMS, Email, <PERSON>ush, Browser, and In-App notifications with automatic scheduling, retry mechanisms, and template management.

## Base URL

```
https://api.wellup.com/v1
```

## Authentication

All API endpoints require authentication via JW<PERSON> token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## API Endpoints

### Notification Preferences

#### Get User Notification Preferences
```http
GET /notification-preferences
```

**Response:**
```json
{
  "id": "uuid",
  "userID": "uuid",
  "smsEnabled": true,
  "smsPhoneNumber": "+1234567890",
  "emailEnabled": true,
  "emailAddress": "<EMAIL>",
  "pushEnabled": true,
  "browserEnabled": true,
  "inAppEnabled": true,
  "reminderHours": [24, 48],
  "quietHoursStart": 22,
  "quietHoursEnd": 8,
  "timezone": "America/New_York",
  "createdAt": "2025-07-24T10:00:00Z",
  "updatedAt": "2025-07-24T10:00:00Z"
}
```

#### Create/Update Notification Preferences
```http
POST /notification-preferences
PUT /notification-preferences
```

**Request Body:**
```json
{
  "smsEnabled": true,
  "smsPhoneNumber": "+1234567890",
  "emailEnabled": true,
  "emailAddress": "<EMAIL>",
  "pushEnabled": true,
  "browserEnabled": true,
  "inAppEnabled": true,
  "reminderHours": [24, 48],
  "quietHoursStart": 22,
  "quietHoursEnd": 8,
  "timezone": "America/New_York"
}
```

#### Test Notifications
```http
POST /notification-preferences/test
```

**Request Body:**
```json
{
  "channels": ["sms", "email", "push"],
  "message": "This is a test notification"
}
```

### Notification Templates

#### List Templates
```http
GET /notification-templates?entityType=task&deliveryChannel=email&isActive=true
```

#### Create Template
```http
POST /notification-templates
```

**Request Body:**
```json
{
  "templateKey": "custom_task_reminder_email",
  "entityType": "task",
  "deliveryChannel": "email",
  "subjectTemplate": "Task Reminder: {entity_title}",
  "bodyTemplate": "Hello {user_name}, your task '{entity_title}' is due {time_until_due}.",
  "variables": ["entity_title", "user_name", "time_until_due"],
  "isActive": true
}
```

#### Preview Template
```http
POST /notification-templates/preview
```

**Request Body:**
```json
{
  "entityType": "task",
  "deliveryChannel": "email",
  "subjectTemplate": "Task Reminder: {entity_title}",
  "bodyTemplate": "Hello {user_name}, your task '{entity_title}' is due {time_until_due}.",
  "variables": ["entity_title", "user_name", "time_until_due"],
  "sampleData": {
    "entityTitle": "Complete project documentation",
    "dueDate": "2025-07-25T14:00:00Z",
    "reminderHours": 24,
    "userName": "John Doe",
    "userEmail": "<EMAIL>"
  }
}
```

### Notification Retry Management

#### Get Queue Status
```http
GET /notification-retry/status
```

**Response:**
```json
{
  "retryQueue": {
    "queueName": "retry-queue",
    "visibleMessages": 5,
    "inFlightMessages": 2,
    "totalMessages": 7
  },
  "deadLetterQueue": {
    "queueName": "dead-letter-queue",
    "visibleMessages": 1,
    "inFlightMessages": 0,
    "totalMessages": 1
  },
  "timestamp": "2025-07-24T10:00:00Z"
}
```

#### Get Notification Schedules
```http
GET /notification-retry/schedules?status=failed&entityType=task&page=1&per=20
```

#### Retry Failed Notification
```http
POST /notification-retry/schedules/{scheduleID}/retry
```

#### Reprocess DLQ Messages
```http
POST /notification-retry/reprocess
```

**Request Body:**
```json
{
  "maxMessages": 10,
  "reason": "Manual reprocessing after system maintenance"
}
```

### Notification History

#### Get User Notifications
```http
GET /notifications?read=false&kind=reminder&page=1&per=20
```

#### Mark Notification as Read
```http
PUT /notifications/{notificationID}/read
```

## Webhook Events

The notification service can send webhook events for monitoring and integration purposes:

### Notification Delivered
```json
{
  "event": "notification.delivered",
  "timestamp": "2025-07-24T10:00:00Z",
  "data": {
    "scheduleId": "uuid",
    "userId": "uuid",
    "entityType": "task",
    "entityId": "uuid",
    "deliveryChannel": "email",
    "success": true,
    "messageId": "external-message-id"
  }
}
```

### Notification Failed
```json
{
  "event": "notification.failed",
  "timestamp": "2025-07-24T10:00:00Z",
  "data": {
    "scheduleId": "uuid",
    "userId": "uuid",
    "entityType": "task",
    "entityId": "uuid",
    "deliveryChannel": "sms",
    "success": false,
    "error": "Invalid phone number",
    "retryAttempt": 1
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": true,
  "reason": "Validation failed",
  "details": {
    "field": "emailAddress",
    "message": "Invalid email format"
  }
}
```

### Common HTTP Status Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource already exists
- `422 Unprocessable Entity` - Validation failed
- `500 Internal Server Error` - Server error

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Notification Preferences**: 100 requests per hour per user
- **Template Management**: 50 requests per hour per user
- **Test Notifications**: 10 requests per hour per user
- **Retry Operations**: 20 requests per hour per user

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1627123200
```

## Template Variables

Available variables for notification templates:

| Variable | Description | Example |
|----------|-------------|---------|
| `entity_title` | Title of the entity | "Complete project documentation" |
| `entity_type` | Type of entity | "task" |
| `entity_type_capitalized` | Capitalized entity type | "Task" |
| `due_date` | Full formatted due date | "July 25, 2025 at 2:00 PM" |
| `due_date_short` | Short formatted due date | "7/25/25" |
| `reminder_hours` | Hours before due date | "24" |
| `time_until_due` | Human readable time | "tomorrow" |
| `user_name` | User's first name | "John" |
| `user_email` | User's email address | "<EMAIL>" |
| `app_url` | Application URL | "https://app.wellup.com" |
| `organization_name` | Organization name | "Wellup" |

## SDK Examples

### JavaScript/TypeScript
```typescript
import { WellupNotificationClient } from '@wellup/notification-sdk';

const client = new WellupNotificationClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.wellup.com/v1'
});

// Update notification preferences
await client.preferences.update({
  smsEnabled: true,
  emailEnabled: true,
  reminderHours: [24, 48]
});

// Test notifications
await client.preferences.test({
  channels: ['email', 'sms'],
  message: 'Test notification'
});
```

### Python
```python
from wellup_notification import NotificationClient

client = NotificationClient(
    api_key='your-api-key',
    base_url='https://api.wellup.com/v1'
)

# Update preferences
client.preferences.update({
    'sms_enabled': True,
    'email_enabled': True,
    'reminder_hours': [24, 48]
})

# Get queue status
status = client.retry.get_queue_status()
print(f"Retry queue has {status['retry_queue']['total_messages']} messages")
```
