//
//  NotifiableEntityExtensions.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent

// MARK: - TaskModel Extension
extension TaskModel: NotifiableEntity {
    var entityType: EntityType {
        return .task
    }
    
    var dueDate: Date {
        if let dueAtEpoc = self.dueAtEpoc {
            return Date(timeIntervalSince1970: TimeInterval(dueAtEpoc))
        }
        return Date().addingTimeInterval(86400) // Default to 24 hours from now
    }
    
    var assignedUserIDs: [UUID] {
        var userIDs: [UUID] = []
        
        // Add creator if exists
        if let creatorID = self.$creator.id {
            userIDs.append(creatorID)
        }
        
        // Add assignee if exists
        if let assigneeID = self.$assignee.id {
            userIDs.append(assigneeID)
        }
        
        // Remove duplicates
        return Array(Set(userIDs))
    }
}

// MARK: - Goal Extension
extension Goal: NotifiableEntity {
    var entityType: EntityType {
        return .goal
    }
    
    var dueDate: Date {
        return self.targetDate
    }
    
    var assignedUserIDs: [UUID] {
        // Goals are typically assigned to the care plan's member and care team
        // For now, we'll need to fetch the care plan to get the member ID
        // This will be improved when we add proper user assignment to goals
        return []
    }
}

// MARK: - Intervention Extension
extension Intervention: NotifiableEntity {
    var entityType: EntityType {
        return .intervention
    }
    
    var assignedUserIDs: [UUID] {
        var userIDs: [UUID] = []
        
        // Add responsible party if it's a valid UUID
        if let responsiblePartyId = self.responsiblePartyId,
           let uuid = UUID(uuidString: responsiblePartyId) {
            userIDs.append(uuid)
        }
        
        return userIDs
    }
}

// MARK: - CarePlanFollowUp Extension
extension CarePlanFollowUp: NotifiableEntity {
    var title: String {
        return "Follow-up: \(self.type)"
    }
    
    var entityType: EntityType {
        return .followUp
    }
    
    var dueDate: Date {
        return self.datetime
    }
    
    var assignedUserIDs: [UUID] {
        // Follow-ups are typically assigned to care team members
        // For now, return empty array - this will be improved when we add proper assignment
        return []
    }
}

// MARK: - Helper Extensions for Fetching Related Users
extension TaskModel {
    func fetchAssignedUsers(on db: Database) -> EventLoopFuture<[User]> {
        var futures: [EventLoopFuture<User?>] = []
        
        // Fetch creator
        if self.$creator.id != nil {
            futures.append(self.$creator.load(on: db).map { self.creator })
        }
        
        // Fetch assignee
        if self.$assignee.id != nil {
            futures.append(self.$assignee.load(on: db).map { self.assignee })
        }
        
        return EventLoopFuture.whenAllSucceed(futures, on: db.eventLoop)
            .map { users in
                return users.compactMap { $0 }
            }
    }
}

extension Goal {
    func fetchAssignedUsers(on db: Database) -> EventLoopFuture<[User]> {
        // Load the care plan and then get the member and care team
        return self.$carePlan.load(on: db)
            .flatMap { _ in
                // For now, return empty array
                // This will be improved when we add proper user assignment to goals
                return db.eventLoop.makeSucceededFuture([])
            }
    }
}

extension Intervention {
    func fetchAssignedUsers(on db: Database) -> EventLoopFuture<[User]> {
        guard let responsiblePartyId = self.responsiblePartyId,
              let uuid = UUID(uuidString: responsiblePartyId) else {
            return db.eventLoop.makeSucceededFuture([])
        }
        
        return User.find(uuid, on: db)
            .map { user in
                return user != nil ? [user!] : []
            }
    }
}

extension CarePlanFollowUp {
    func fetchAssignedUsers(on db: Database) -> EventLoopFuture<[User]> {
        // Load the care plan and then get the care team
        return self.$carePlan.load(on: db)
            .flatMap { _ in
                // For now, return empty array
                // This will be improved when we add proper care team assignment
                return db.eventLoop.makeSucceededFuture([])
            }
    }
}

// MARK: - Notification Scheduling Extensions
extension TaskModel {
    func scheduleNotifications(on req: Request) -> EventLoopFuture<Void> {
        return req.notificationService.scheduleReminders(for: self, on: req.db, eventLoop: req.eventLoop)
    }
    
    func cancelNotifications(on req: Request) -> EventLoopFuture<Void> {
        guard let id = self.id else {
            return req.eventLoop.makeSucceededFuture(())
        }
        return req.notificationService.cancelReminders(for: self.entityType, entityID: id, on: req.db, eventLoop: req.eventLoop)
    }
}

extension Goal {
    func scheduleNotifications(on req: Request) -> EventLoopFuture<Void> {
        return req.notificationService.scheduleReminders(for: self, on: req.db, eventLoop: req.eventLoop)
    }
    
    func cancelNotifications(on req: Request) -> EventLoopFuture<Void> {
        guard let id = self.id else {
            return req.eventLoop.makeSucceededFuture(())
        }
        return req.notificationService.cancelReminders(for: self.entityType, entityID: id, on: req.db, eventLoop: req.eventLoop)
    }
}

extension Intervention {
    func scheduleNotifications(on req: Request) -> EventLoopFuture<Void> {
        return req.notificationService.scheduleReminders(for: self, on: req.db, eventLoop: req.eventLoop)
    }
    
    func cancelNotifications(on req: Request) -> EventLoopFuture<Void> {
        guard let id = self.id else {
            return req.eventLoop.makeSucceededFuture(())
        }
        return req.notificationService.cancelReminders(for: self.entityType, entityID: id, on: req.db, eventLoop: req.eventLoop)
    }
}

extension CarePlanFollowUp {
    func scheduleNotifications(on req: Request) -> EventLoopFuture<Void> {
        return req.notificationService.scheduleReminders(for: self, on: req.db, eventLoop: req.eventLoop)
    }
    
    func cancelNotifications(on req: Request) -> EventLoopFuture<Void> {
        guard let id = self.id else {
            return req.eventLoop.makeSucceededFuture(())
        }
        return req.notificationService.cancelReminders(for: self.entityType, entityID: id, on: req.db, eventLoop: req.eventLoop)
    }
}
