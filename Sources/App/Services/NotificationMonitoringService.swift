//
//  NotificationMonitoringService.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent
import SotoCloudWatch

// MARK: - Monitoring Service
final class NotificationMonitoringService {
    private let cloudWatchClient: CloudWatch
    private let logger: Logger
    private let namespace: String
    
    init(awsClient: AWSClient, logger: Logger) {
        self.cloudWatchClient = CloudWatch(client: awsClient, region: .useast1)
        self.logger = logger
        self.namespace = Environment.get("CLOUDWATCH_NAMESPACE") ?? "Wellup/Notifications"
    }
    
    // MARK: - Delivery Metrics
    func recordDeliverySuccess(
        channel: DeliveryChannel,
        entityType: EntityType,
        userID: UUID
    ) -> EventLoopFuture<Void> {
        let metrics = [
            createMetric(
                name: "NotificationDelivered",
                value: 1.0,
                unit: .count,
                dimensions: [
                    ("Channel", channel.rawValue),
                    ("EntityType", entityType.rawValue),
                    ("Status", "Success")
                ]
            ),
            createMetric(
                name: "NotificationsByChannel",
                value: 1.0,
                unit: .count,
                dimensions: [("Channel", channel.rawValue)]
            )
        ]
        
        return putMetrics(metrics)
            .map { _ in
                self.logger.info("Recorded delivery success metric", metadata: [
                    "channel": .string(channel.rawValue),
                    "entityType": .string(entityType.rawValue),
                    "userID": .string(userID.uuidString)
                ])
            }
    }
    
    func recordDeliveryFailure(
        channel: DeliveryChannel,
        entityType: EntityType,
        error: String,
        userID: UUID
    ) -> EventLoopFuture<Void> {
        let metrics = [
            createMetric(
                name: "NotificationDelivered",
                value: 1.0,
                unit: .count,
                dimensions: [
                    ("Channel", channel.rawValue),
                    ("EntityType", entityType.rawValue),
                    ("Status", "Failed")
                ]
            ),
            createMetric(
                name: "NotificationErrors",
                value: 1.0,
                unit: .count,
                dimensions: [
                    ("Channel", channel.rawValue),
                    ("ErrorType", categorizeError(error))
                ]
            )
        ]
        
        return putMetrics(metrics)
            .map { _ in
                self.logger.error("Recorded delivery failure metric", metadata: [
                    "channel": .string(channel.rawValue),
                    "entityType": .string(entityType.rawValue),
                    "error": .string(error),
                    "userID": .string(userID.uuidString)
                ])
            }
    }
    
    // MARK: - Schedule Metrics
    func recordScheduleCreated(entityType: EntityType, reminderHours: Int) -> EventLoopFuture<Void> {
        let metrics = [
            createMetric(
                name: "NotificationScheduled",
                value: 1.0,
                unit: .count,
                dimensions: [
                    ("EntityType", entityType.rawValue),
                    ("ReminderHours", "\(reminderHours)")
                ]
            )
        ]
        
        return putMetrics(metrics)
    }
    
    func recordScheduleProcessed(
        entityType: EntityType,
        processingTimeMs: Double,
        success: Bool
    ) -> EventLoopFuture<Void> {
        let metrics = [
            createMetric(
                name: "ScheduleProcessingTime",
                value: processingTimeMs,
                unit: .milliseconds,
                dimensions: [("EntityType", entityType.rawValue)]
            ),
            createMetric(
                name: "ScheduleProcessed",
                value: 1.0,
                unit: .count,
                dimensions: [
                    ("EntityType", entityType.rawValue),
                    ("Status", success ? "Success" : "Failed")
                ]
            )
        ]
        
        return putMetrics(metrics)
    }
    
    // MARK: - Queue Metrics
    func recordQueueMetrics(
        retryQueueSize: Int,
        dlqSize: Int
    ) -> EventLoopFuture<Void> {
        let metrics = [
            createMetric(
                name: "RetryQueueSize",
                value: Double(retryQueueSize),
                unit: .count,
                dimensions: []
            ),
            createMetric(
                name: "DeadLetterQueueSize",
                value: Double(dlqSize),
                unit: .count,
                dimensions: []
            )
        ]
        
        return putMetrics(metrics)
    }
    
    // MARK: - User Metrics
    func recordUserPreferencesUpdate(userID: UUID, enabledChannels: [DeliveryChannel]) -> EventLoopFuture<Void> {
        let metrics = enabledChannels.map { channel in
            createMetric(
                name: "UserPreferencesEnabled",
                value: 1.0,
                unit: .count,
                dimensions: [("Channel", channel.rawValue)]
            )
        }
        
        return putMetrics(metrics)
    }
    
    // MARK: - Performance Metrics
    func recordAPILatency(
        endpoint: String,
        method: String,
        statusCode: Int,
        latencyMs: Double
    ) -> EventLoopFuture<Void> {
        let metrics = [
            createMetric(
                name: "APILatency",
                value: latencyMs,
                unit: .milliseconds,
                dimensions: [
                    ("Endpoint", endpoint),
                    ("Method", method),
                    ("StatusCode", "\(statusCode)")
                ]
            ),
            createMetric(
                name: "APIRequests",
                value: 1.0,
                unit: .count,
                dimensions: [
                    ("Endpoint", endpoint),
                    ("Method", method),
                    ("StatusCode", "\(statusCode)")
                ]
            )
        ]
        
        return putMetrics(metrics)
    }
    
    // MARK: - Template Metrics
    func recordTemplateUsage(
        templateKey: String,
        entityType: EntityType,
        deliveryChannel: DeliveryChannel
    ) -> EventLoopFuture<Void> {
        let metrics = [
            createMetric(
                name: "TemplateUsage",
                value: 1.0,
                unit: .count,
                dimensions: [
                    ("TemplateKey", templateKey),
                    ("EntityType", entityType.rawValue),
                    ("Channel", deliveryChannel.rawValue)
                ]
            )
        ]
        
        return putMetrics(metrics)
    }
    
    // MARK: - Custom Metrics
    func recordCustomMetric(
        name: String,
        value: Double,
        unit: CloudWatch.StandardUnit,
        dimensions: [(String, String)] = []
    ) -> EventLoopFuture<Void> {
        let metric = createMetric(name: name, value: value, unit: unit, dimensions: dimensions)
        return putMetrics([metric])
    }
    
    // MARK: - Batch Metrics
    func recordBatchMetrics(_ metrics: [CloudWatch.MetricDatum]) -> EventLoopFuture<Void> {
        return putMetrics(metrics)
    }
    
    // MARK: - Health Check Metrics
    func recordHealthCheck(
        component: String,
        healthy: Bool,
        responseTimeMs: Double? = nil
    ) -> EventLoopFuture<Void> {
        var metrics = [
            createMetric(
                name: "HealthCheck",
                value: healthy ? 1.0 : 0.0,
                unit: .count,
                dimensions: [("Component", component)]
            )
        ]
        
        if let responseTime = responseTimeMs {
            metrics.append(
                createMetric(
                    name: "HealthCheckResponseTime",
                    value: responseTime,
                    unit: .milliseconds,
                    dimensions: [("Component", component)]
                )
            )
        }
        
        return putMetrics(metrics)
    }
    
    // MARK: - Private Helper Methods
    private func createMetric(
        name: String,
        value: Double,
        unit: CloudWatch.StandardUnit,
        dimensions: [(String, String)]
    ) -> CloudWatch.MetricDatum {
        let cloudWatchDimensions = dimensions.map { (key, value) in
            CloudWatch.Dimension(name: key, value: value)
        }
        
        return CloudWatch.MetricDatum(
            dimensions: cloudWatchDimensions.isEmpty ? nil : cloudWatchDimensions,
            metricName: name,
            timestamp: Date(),
            unit: unit,
            value: value
        )
    }
    
    private func putMetrics(_ metrics: [CloudWatch.MetricDatum]) -> EventLoopFuture<Void> {
        // CloudWatch has a limit of 20 metrics per request
        let batches = metrics.chunked(into: 20)
        
        let batchFutures = batches.map { batch in
            let putMetricDataInput = CloudWatch.PutMetricDataInput(
                metricData: batch,
                namespace: self.namespace
            )
            
            return self.cloudWatchClient.putMetricData(putMetricDataInput)
                .map { _ in () }
                .recover { error in
                    self.logger.error("Failed to put CloudWatch metrics: \(error)")
                    // Don't fail the operation if metrics can't be sent
                }
        }
        
        return EventLoopFuture.andAllSucceed(batchFutures, on: cloudWatchClient.eventLoopGroup.next())
    }
    
    private func categorizeError(_ error: String) -> String {
        let lowercaseError = error.lowercased()
        
        if lowercaseError.contains("timeout") || lowercaseError.contains("timed out") {
            return "Timeout"
        } else if lowercaseError.contains("network") || lowercaseError.contains("connection") {
            return "Network"
        } else if lowercaseError.contains("authentication") || lowercaseError.contains("unauthorized") {
            return "Authentication"
        } else if lowercaseError.contains("rate limit") || lowercaseError.contains("throttle") {
            return "RateLimit"
        } else if lowercaseError.contains("invalid") || lowercaseError.contains("malformed") {
            return "Validation"
        } else if lowercaseError.contains("not found") || lowercaseError.contains("missing") {
            return "NotFound"
        } else {
            return "Other"
        }
    }
}

// MARK: - Array Extension for Chunking
extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}

// MARK: - Monitoring Middleware
struct NotificationMonitoringMiddleware: AsyncMiddleware {
    private let monitoringService: NotificationMonitoringService
    
    init(monitoringService: NotificationMonitoringService) {
        self.monitoringService = monitoringService
    }
    
    func respond(to request: Request, chainingTo next: AsyncResponder) async throws -> Response {
        let startTime = Date()
        
        do {
            let response = try await next.respond(to: request)
            let endTime = Date()
            let latency = endTime.timeIntervalSince(startTime) * 1000 // Convert to milliseconds
            
            // Record API metrics for notification endpoints
            if request.url.path.contains("notification") {
                _ = monitoringService.recordAPILatency(
                    endpoint: request.url.path,
                    method: request.method.rawValue,
                    statusCode: Int(response.status.code),
                    latencyMs: latency
                )
            }
            
            return response
        } catch {
            let endTime = Date()
            let latency = endTime.timeIntervalSince(startTime) * 1000
            
            // Record error metrics
            if request.url.path.contains("notification") {
                _ = monitoringService.recordAPILatency(
                    endpoint: request.url.path,
                    method: request.method.rawValue,
                    statusCode: 500,
                    latencyMs: latency
                )
            }
            
            throw error
        }
    }
}

// MARK: - Application Extension
extension Application {
    var notificationMonitoring: NotificationMonitoringService {
        return NotificationMonitoringService(awsClient: self.aws.client, logger: self.logger)
    }
}

extension Request {
    var notificationMonitoring: NotificationMonitoringService {
        return self.application.notificationMonitoring
    }
}
