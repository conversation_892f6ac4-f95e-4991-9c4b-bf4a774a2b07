//
//  NotificationDeliveryService.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent
import SotoSNS
import SotoPinpoint

// MARK: - Delivery Result
struct DeliveryResult {
    let channel: DeliveryChannel
    let success: Bool
    let messageId: String?
    let error: String?
    let responseData: [String: Any]?
}

// MARK: - Multi-Channel Delivery Service
final class NotificationDeliveryService {
    private let snsClient: SNS
    private let pinpointClient: Pinpoint
    private let logger: Logger
    
    init(awsClient: AWSClient, logger: Logger) {
        self.snsClient = SNS(client: awsClient, region: .useast1)
        self.pinpointClient = Pinpoint(client: awsClient, region: .useast1)
        self.logger = logger
    }
    
    // MARK: - Main Delivery Method
    func deliverNotification(_ payload: NotificationPayload, via channels: [DeliveryChannel], on req: Request) -> EventLoopFuture<[DeliveryResult]> {
        logger.info("Delivering notification to \(channels.count) channels for user \(payload.userID)")
        
        // Create in-app notification first (always succeeds)
        let inAppFuture = createInAppNotification(payload, on: req)
        
        // Deliver to each channel
        let deliveryFutures = channels.map { channel in
            self.deliverToChannel(payload, channel: channel, on: req)
        }
        
        return inAppFuture.flatMap { _ in
            return EventLoopFuture.whenAllComplete(deliveryFutures, on: req.eventLoop)
                .flatMap { results in
                    let deliveryResults = results.compactMap { result in
                        switch result {
                        case .success(let deliveryResult):
                            // Record delivery metrics
                            if deliveryResult.success {
                                _ = req.notificationMonitoring.recordDeliverySuccess(
                                    channel: deliveryResult.channel,
                                    entityType: payload.entityType,
                                    userID: payload.userID
                                )
                            } else {
                                _ = req.notificationMonitoring.recordDeliveryFailure(
                                    channel: deliveryResult.channel,
                                    entityType: payload.entityType,
                                    error: deliveryResult.error ?? "Unknown error",
                                    userID: payload.userID
                                )
                            }
                            return deliveryResult
                        case .failure(let error):
                            self.logger.error("Delivery failed: \(error)")
                            return DeliveryResult(
                                channel: .inApp, // Default channel for failed deliveries
                                success: false,
                                messageId: nil,
                                error: error.localizedDescription,
                                responseData: nil
                            )
                        }
                    }

                    // Check if all deliveries failed and send to retry queue if needed
                    let hasSuccessfulDelivery = deliveryResults.contains { $0.success }

                    if !hasSuccessfulDelivery && !channels.isEmpty {
                        return self.sendToRetryQueue(payload: payload, on: req)
                            .map { _ in deliveryResults }
                    }

                    return req.eventLoop.makeSucceededFuture(deliveryResults)
                }
        }
    }
    
    // MARK: - Channel-Specific Delivery Methods
    private func deliverToChannel(_ payload: NotificationPayload, channel: DeliveryChannel, on req: Request) -> EventLoopFuture<DeliveryResult> {
        switch channel {
        case .sms:
            return deliverSMS(payload, on: req)
        case .email:
            return deliverEmail(payload, on: req)
        case .push:
            return deliverPush(payload, on: req)
        case .browser:
            return deliverBrowser(payload, on: req)
        case .inApp:
            return req.eventLoop.makeSucceededFuture(
                DeliveryResult(channel: .inApp, success: true, messageId: nil, error: nil, responseData: nil)
            )
        }
    }
    
    // MARK: - SMS Delivery
    private func deliverSMS(_ payload: NotificationPayload, on req: Request) -> EventLoopFuture<DeliveryResult> {
        return getUserPhoneNumber(userID: payload.userID, on: req.db)
            .flatMap { phoneNumber in
                guard let phone = phoneNumber else {
                    return req.eventLoop.makeSucceededFuture(
                        DeliveryResult(
                            channel: .sms,
                            success: false,
                            messageId: nil,
                            error: "No phone number found for user",
                            responseData: nil
                        )
                    )
                }

                // Get user for template rendering
                return User.find(payload.userID, on: req.db)
                    .flatMap { user in
                        // Render SMS template
                        return req.notificationTemplateService.renderNotification(
                            for: payload,
                            deliveryChannel: .sms,
                            user: user,
                            on: req.db
                        ).flatMap { renderedTemplate in
                            let smsMessage = self.formatSMSMessage(payload, renderedTemplate: renderedTemplate)
                            return self.sendTwilioSMS(to: phone, message: smsMessage, on: req)
                        }
                    }
            }
    }
    
    // MARK: - Email Delivery
    private func deliverEmail(_ payload: NotificationPayload, on req: Request) -> EventLoopFuture<DeliveryResult> {
        return getUserEmail(userID: payload.userID, on: req.db)
            .flatMap { email in
                guard let emailAddress = email else {
                    return req.eventLoop.makeSucceededFuture(
                        DeliveryResult(
                            channel: .email,
                            success: false,
                            messageId: nil,
                            error: "No email address found for user",
                            responseData: nil
                        )
                    )
                }

                // Get user for template rendering
                return User.find(payload.userID, on: req.db)
                    .flatMap { user in
                        // Render email template
                        return req.notificationTemplateService.renderNotification(
                            for: payload,
                            deliveryChannel: .email,
                            user: user,
                            on: req.db
                        ).flatMap { renderedTemplate in
                            return self.sendSendGridEmail(
                                to: emailAddress,
                                subject: renderedTemplate.subject ?? payload.title,
                                body: renderedTemplate.body,
                                payload: payload,
                                on: req
                            )
                        }
                    }
            }
    }
    
    // MARK: - Push Notification Delivery
    private func deliverPush(_ payload: NotificationPayload, on req: Request) -> EventLoopFuture<DeliveryResult> {
        return getUserDevices(userID: payload.userID, on: req.db)
            .flatMap { devices in
                if devices.isEmpty {
                    return req.eventLoop.makeSucceededFuture(
                        DeliveryResult(
                            channel: .push,
                            success: false,
                            messageId: nil,
                            error: "No devices found for user",
                            responseData: nil
                        )
                    )
                }
                
                let pushFutures = devices.compactMap { device in
                    device.arn
                }.map { arn in
                    self.sendSNSPush(to: arn, payload: payload, on: req.eventLoop)
                }
                
                return EventLoopFuture.whenAllComplete(pushFutures, on: req.eventLoop)
                    .map { results in
                        let successCount = results.filter { result in
                            if case .success = result { return true }
                            return false
                        }.count
                        
                        return DeliveryResult(
                            channel: .push,
                            success: successCount > 0,
                            messageId: "\(successCount)/\(results.count) devices",
                            error: successCount == 0 ? "Failed to deliver to all devices" : nil,
                            responseData: ["devices_targeted": results.count, "devices_successful": successCount]
                        )
                    }
            }
    }
    
    // MARK: - Browser Notification Delivery
    private func deliverBrowser(_ payload: NotificationPayload, on req: Request) -> EventLoopFuture<DeliveryResult> {
        // Use Amazon Pinpoint for browser push notifications
        let applicationId = Environment.get("PINPOINT_APPLICATION_ID") ?? ""
        
        let messageRequest = Pinpoint.MessageRequest(
            addresses: [payload.userID.uuidString: Pinpoint.AddressConfiguration(
                channelType: .gcm // or .apns for iOS web push
            )],
            messageConfiguration: Pinpoint.DirectMessageConfiguration(
                gcmMessage: Pinpoint.GCMMessage(
                    action: .openApp,
                    body: payload.message,
                    title: payload.title,
                    data: payload.metadata
                )
            )
        )
        
        let sendRequest = Pinpoint.SendMessagesRequest(
            applicationId: applicationId,
            messageRequest: messageRequest
        )
        
        return pinpointClient.sendMessages(sendRequest)
            .map { response in
                let success = response.messageResponse.result?[payload.userID.uuidString]?.deliveryStatus == .successful
                
                return DeliveryResult(
                    channel: .browser,
                    success: success,
                    messageId: response.messageResponse.requestId,
                    error: success ? nil : "Browser push delivery failed",
                    responseData: [
                        "request_id": response.messageResponse.requestId ?? "",
                        "application_id": response.messageResponse.applicationId ?? ""
                    ]
                )
            }
            .recover { error in
                return DeliveryResult(
                    channel: .browser,
                    success: false,
                    messageId: nil,
                    error: error.localizedDescription,
                    responseData: nil
                )
            }
    }
    
    // MARK: - In-App Notification Creation
    private func createInAppNotification(_ payload: NotificationPayload, on req: Request) -> EventLoopFuture<Void> {
        // Get user for template rendering
        return User.find(payload.userID, on: req.db)
            .flatMap { user in
                // Render template for in-app notification
                return req.notificationTemplateService.renderNotification(
                    for: payload,
                    deliveryChannel: .inApp,
                    user: user,
                    on: req.db
                ).flatMap { renderedTemplate in
                    let notification = UserNotification(
                        title: renderedTemplate.subject ?? payload.title,
                        kind: NotificationKind.reminder.rawValue,
                        message: renderedTemplate.body,
                        read: false,
                        userID: payload.userID,
                        meta: payload.metadata.map { MetaData(data: $0) },
                        entityType: payload.entityType.rawValue,
                        entityID: payload.entityID,
                        deliveryChannel: DeliveryChannel.inApp.rawValue,
                        deliveryStatus: DeliveryStatus.sent.rawValue,
                        scheduledFor: Date()
                    )

                    return notification.save(on: req.db)
                        .map { _ in
                            self.logger.info("Created in-app notification for user \(payload.userID)")
                        }
                }
            }
    }
    
    // MARK: - Helper Methods
    private func getUserPhoneNumber(userID: UUID, on db: Database) -> EventLoopFuture<String?> {
        return UserNotificationPreferences.query(on: db)
            .filter(\.$user.$id == userID)
            .first()
            .map { preferences in
                return preferences?.smsPhoneNumber
            }
    }
    
    private func getUserEmail(userID: UUID, on db: Database) -> EventLoopFuture<String?> {
        return UserNotificationPreferences.query(on: db)
            .filter(\.$user.$id == userID)
            .first()
            .flatMap { preferences in
                if let email = preferences?.emailAddress {
                    return db.eventLoop.makeSucceededFuture(email)
                }
                
                // Fallback to user's primary email
                return User.find(userID, on: db)
                    .map { user in
                        return user?.email
                    }
            }
    }
    
    private func getUserDevices(userID: UUID, on db: Database) -> EventLoopFuture<[Device]> {
        return Device.query(on: db)
            .filter(\.$userID == userID.uuidString)
            .all()
    }
    
    private func formatSMSMessage(_ payload: NotificationPayload, renderedTemplate: RenderedTemplate) -> String {
        return renderedTemplate.body
    }
    
    private func sendTwilioSMS(to phoneNumber: String, message: String, on req: Request) -> EventLoopFuture<DeliveryResult> {
        // Use existing TwilioController functionality
        let twilioController = TwilioController()
        
        // Create a mock request with SMS data
        let smsData = [
            "to": phoneNumber,
            "body": message
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: smsData)
            let buffer = ByteBuffer(data: jsonData)
            
            var headers = HTTPHeaders()
            headers.add(name: .contentType, value: "application/json")
            
            let mockRequest = Request(
                application: req.application,
                headers: headers,
                collectedBody: buffer,
                on: req.eventLoop
            )
            
            return twilioController.sendSms(req: mockRequest)
                .map { response in
                    return DeliveryResult(
                        channel: .sms,
                        success: response.status.code < 400,
                        messageId: nil,
                        error: response.status.code >= 400 ? "SMS delivery failed" : nil,
                        responseData: ["status_code": response.status.code]
                    )
                }
                .recover { error in
                    return DeliveryResult(
                        channel: .sms,
                        success: false,
                        messageId: nil,
                        error: error.localizedDescription,
                        responseData: nil
                    )
                }
        } catch {
            return req.eventLoop.makeSucceededFuture(
                DeliveryResult(
                    channel: .sms,
                    success: false,
                    messageId: nil,
                    error: "Failed to encode SMS data",
                    responseData: nil
                )
            )
        }
    }
    
    private func sendSendGridEmail(to email: String, subject: String, body: String, payload: NotificationPayload, on req: Request) -> EventLoopFuture<DeliveryResult> {
        // Use existing TwilioController email functionality
        let twilioController = TwilioController()
        
        // Create email template data
        let emailData = NotificationEmailData(
            email: email,
            subject: subject,
            body: body,
            entityType: payload.entityType.rawValue,
            entityTitle: payload.metadata?["entity_title"] ?? "",
            dueDate: payload.dueDate
        )
        
        return twilioController.sendNotificationEmail(emailData: emailData, on: req)
            .map { response in
                return DeliveryResult(
                    channel: .email,
                    success: response.status.code < 400,
                    messageId: nil,
                    error: response.status.code >= 400 ? "Email delivery failed" : nil,
                    responseData: ["status_code": response.status.code]
                )
            }
            .recover { error in
                return DeliveryResult(
                    channel: .email,
                    success: false,
                    messageId: nil,
                    error: error.localizedDescription,
                    responseData: nil
                )
            }
    }
    
    private func sendSNSPush(to endpointArn: String, payload: NotificationPayload, on eventLoop: EventLoop) -> EventLoopFuture<Void> {
        let message = [
            "default": payload.message,
            "APNS": [
                "aps": [
                    "alert": [
                        "title": payload.title,
                        "body": payload.message
                    ],
                    "sound": "default",
                    "badge": 1
                ],
                "custom_data": payload.metadata ?? [:]
            ]
        ]
        
        guard let messageData = try? JSONSerialization.data(withJSONObject: message),
              let messageString = String(data: messageData, encoding: .utf8) else {
            return eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Failed to encode push message"))
        }
        
        let publishInput = SNS.PublishInput(
            message: messageString,
            messageStructure: "json",
            targetArn: endpointArn
        )
        
        return snsClient.publish(publishInput)
            .map { _ in () }
    }

    // MARK: - Retry Queue Integration
    private func sendToRetryQueue(payload: NotificationPayload, on req: Request) -> EventLoopFuture<Void> {
        return req.sqsRetryService.sendToRetryQueue(
            scheduleId: payload.metadata?["schedule_id"] ?? payload.entityID.uuidString,
            entityType: payload.entityType.rawValue,
            entityId: payload.entityID.uuidString,
            userId: payload.userID.uuidString,
            reminderHoursBefore: payload.reminderHoursBefore,
            originalError: "All delivery channels failed"
        )
    }
}

// MARK: - Email Data Structure
struct NotificationEmailData {
    let email: String
    let subject: String
    let body: String
    let entityType: String
    let entityTitle: String
    let dueDate: Date
}

// MARK: - Application Extension
extension Application {
    var notificationDelivery: NotificationDeliveryService {
        return NotificationDeliveryService(awsClient: self.aws.client, logger: self.logger)
    }
}

extension Request {
    var notificationDelivery: NotificationDeliveryService {
        return self.application.notificationDelivery
    }
}
