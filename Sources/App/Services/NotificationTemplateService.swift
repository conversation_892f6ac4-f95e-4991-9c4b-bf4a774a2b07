//
//  NotificationTemplateService.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent

// MARK: - Notification Template Service
final class NotificationTemplateService {
    private let logger: Logger
    
    init(logger: Logger) {
        self.logger = logger
    }
    
    // MARK: - Get Template
    func getTemplate(
        for entityType: EntityType,
        deliveryChannel: DeliveryChannel,
        on db: Database
    ) -> EventLoopFuture<NotificationTemplate?> {
        let templateKey = "\(entityType.rawValue)_reminder_\(deliveryChannel.rawValue)"
        
        return NotificationTemplate.query(on: db)
            .filter(\.$templateKey == templateKey)
            .filter(\.$isActive == true)
            .first()
            .flatMap { template in
                if let template = template {
                    return db.eventLoop.makeSucceededFuture(template)
                }
                
                // Fallback to generic template
                return self.getGenericTemplate(for: deliveryChannel, on: db)
            }
    }
    
    // MARK: - Render Notification
    func renderNotification(
        for payload: NotificationPayload,
        deliveryChannel: DeliveryChannel,
        user: User?,
        on db: Database
    ) -> EventLoopFuture<RenderedTemplate> {
        return getTemplate(for: payload.entityType, deliveryChannel: deliveryChannel, on: db)
            .flatMap { template in
                guard let template = template else {
                    // Create a basic template if none found
                    return db.eventLoop.makeSucceededFuture(
                        self.createBasicTemplate(for: payload, deliveryChannel: deliveryChannel)
                    )
                }
                
                let variables = TemplateVariables(
                    entityTitle: payload.title,
                    entityType: payload.entityType.rawValue,
                    dueDate: payload.dueDate,
                    reminderHours: payload.reminderHoursBefore,
                    userName: user?.firstName,
                    userEmail: user?.email,
                    appUrl: Environment.get("APP_URL") ?? "https://app.wellup.com",
                    organizationName: Environment.get("ORGANIZATION_NAME") ?? "Wellup"
                )
                
                let rendered = NotificationTemplateRenderer.render(template: template, variables: variables)
                return db.eventLoop.makeSucceededFuture(rendered)
            }
    }
    
    // MARK: - Initialize Default Templates
    func initializeDefaultTemplates(on db: Database) -> EventLoopFuture<Void> {
        logger.info("Initializing default notification templates")
        
        let templateFutures = DefaultNotificationTemplates.templates.map { template in
            // Check if template already exists
            NotificationTemplate.query(on: db)
                .filter(\.$templateKey == template.templateKey)
                .first()
                .flatMap { existingTemplate in
                    if existingTemplate == nil {
                        // Create new template
                        return template.save(on: db)
                            .map { _ in
                                self.logger.info("Created template: \(template.templateKey)")
                            }
                    } else {
                        // Template already exists
                        return db.eventLoop.makeSucceededFuture(())
                    }
                }
        }
        
        return EventLoopFuture.andAllSucceed(templateFutures, on: db.eventLoop)
            .map { _ in
                self.logger.info("Default notification templates initialized")
            }
    }
    
    // MARK: - Create Custom Template
    func createTemplate(
        templateKey: String,
        entityType: String,
        deliveryChannel: String,
        subjectTemplate: String?,
        bodyTemplate: String,
        variables: [String],
        on db: Database
    ) -> EventLoopFuture<NotificationTemplate> {
        let template = NotificationTemplate(
            templateKey: templateKey,
            entityType: entityType,
            deliveryChannel: deliveryChannel,
            subjectTemplate: subjectTemplate,
            bodyTemplate: bodyTemplate,
            variables: variables
        )
        
        return template.save(on: db)
            .map { _ in
                self.logger.info("Created custom template: \(templateKey)")
                return template
            }
    }
    
    // MARK: - Update Template
    func updateTemplate(
        id: UUID,
        subjectTemplate: String?,
        bodyTemplate: String,
        variables: [String],
        isActive: Bool,
        on db: Database
    ) -> EventLoopFuture<NotificationTemplate> {
        return NotificationTemplate.find(id, on: db)
            .unwrap(or: Abort(.notFound, reason: "Template not found"))
            .flatMap { template in
                template.subjectTemplate = subjectTemplate
                template.bodyTemplate = bodyTemplate
                template.variables = variables
                template.isActive = isActive
                
                return template.update(on: db)
                    .map { _ in
                        self.logger.info("Updated template: \(template.templateKey)")
                        return template
                    }
            }
    }
    
    // MARK: - Delete Template
    func deleteTemplate(id: UUID, on db: Database) -> EventLoopFuture<Void> {
        return NotificationTemplate.find(id, on: db)
            .unwrap(or: Abort(.notFound, reason: "Template not found"))
            .flatMap { template in
                let templateKey = template.templateKey
                return template.delete(on: db)
                    .map { _ in
                        self.logger.info("Deleted template: \(templateKey)")
                    }
            }
    }
    
    // MARK: - List Templates
    func listTemplates(
        entityType: String? = nil,
        deliveryChannel: String? = nil,
        isActive: Bool? = nil,
        on db: Database
    ) -> EventLoopFuture<[NotificationTemplate]> {
        var query = NotificationTemplate.query(on: db)
        
        if let entityType = entityType {
            query = query.filter(\.$entityType == entityType)
        }
        
        if let deliveryChannel = deliveryChannel {
            query = query.filter(\.$deliveryChannel == deliveryChannel)
        }
        
        if let isActive = isActive {
            query = query.filter(\.$isActive == isActive)
        }
        
        return query.sort(\.$templateKey).all()
    }
    
    // MARK: - Validate Template
    func validateTemplate(_ template: NotificationTemplate) -> [String] {
        var errors: [String] = []
        
        // Check required fields
        if template.templateKey.isEmpty {
            errors.append("Template key is required")
        }
        
        if template.entityType.isEmpty {
            errors.append("Entity type is required")
        }
        
        if template.deliveryChannel.isEmpty {
            errors.append("Delivery channel is required")
        }
        
        if template.bodyTemplate.isEmpty {
            errors.append("Body template is required")
        }
        
        // Validate entity type
        if !EntityType.allCases.map(\.rawValue).contains(template.entityType) && template.entityType != "generic" {
            errors.append("Invalid entity type: \(template.entityType)")
        }
        
        // Validate delivery channel
        if !DeliveryChannel.allCases.map(\.rawValue).contains(template.deliveryChannel) {
            errors.append("Invalid delivery channel: \(template.deliveryChannel)")
        }
        
        // Check for required variables in templates
        let requiredVariables = extractVariablesFromTemplate(template.bodyTemplate)
        if let subjectTemplate = template.subjectTemplate {
            requiredVariables.formUnion(extractVariablesFromTemplate(subjectTemplate))
        }
        
        let missingVariables = requiredVariables.subtracting(Set(template.variables))
        if !missingVariables.isEmpty {
            errors.append("Missing variables in template definition: \(missingVariables.joined(separator: ", "))")
        }
        
        return errors
    }
    
    // MARK: - Private Helper Methods
    private func getGenericTemplate(for deliveryChannel: DeliveryChannel, on db: Database) -> EventLoopFuture<NotificationTemplate?> {
        let genericKey = "generic_reminder_\(deliveryChannel.rawValue)"
        
        return NotificationTemplate.query(on: db)
            .filter(\.$templateKey == genericKey)
            .filter(\.$isActive == true)
            .first()
    }
    
    private func createBasicTemplate(for payload: NotificationPayload, deliveryChannel: DeliveryChannel) -> RenderedTemplate {
        let timeDescription = payload.reminderHoursBefore == 24 ? "tomorrow" : "in \(payload.reminderHoursBefore) hours"
        
        let subject: String?
        let body: String
        
        switch deliveryChannel {
        case .sms:
            subject = nil
            body = "Reminder: Your \(payload.entityType.rawValue) '\(payload.title)' is due \(timeDescription)."
        case .email:
            subject = "\(payload.entityType.rawValue.capitalized) Reminder: \(payload.title)"
            body = "This is a reminder that your \(payload.entityType.rawValue) '\(payload.title)' is due \(timeDescription)."
        case .push, .browser:
            subject = "\(payload.entityType.rawValue.capitalized) Due \(timeDescription.capitalized)"
            body = "Your \(payload.entityType.rawValue) '\(payload.title)' is due \(timeDescription)."
        case .inApp:
            subject = "\(payload.entityType.rawValue.capitalized) Reminder"
            body = "Your \(payload.entityType.rawValue) '\(payload.title)' is due \(timeDescription)."
        }
        
        return RenderedTemplate(
            subject: subject,
            body: body,
            templateKey: "basic_\(deliveryChannel.rawValue)",
            deliveryChannel: deliveryChannel.rawValue
        )
    }
    
    private func extractVariablesFromTemplate(_ template: String) -> Set<String> {
        var variables = Set<String>()
        
        // Extract {{variable}} patterns
        let doublePattern = #"\{\{(\w+)\}\}"#
        let doubleRegex = try! NSRegularExpression(pattern: doublePattern)
        let doubleMatches = doubleRegex.matches(in: template, range: NSRange(template.startIndex..., in: template))
        
        for match in doubleMatches {
            if let range = Range(match.range(at: 1), in: template) {
                variables.insert(String(template[range]))
            }
        }
        
        // Extract {variable} patterns
        let singlePattern = #"\{(\w+)\}"#
        let singleRegex = try! NSRegularExpression(pattern: singlePattern)
        let singleMatches = singleRegex.matches(in: template, range: NSRange(template.startIndex..., in: template))
        
        for match in singleMatches {
            if let range = Range(match.range(at: 1), in: template) {
                variables.insert(String(template[range]))
            }
        }
        
        return variables
    }
}

// MARK: - Application Extension
extension Application {
    var notificationTemplateService: NotificationTemplateService {
        return NotificationTemplateService(logger: self.logger)
    }
}

extension Request {
    var notificationTemplateService: NotificationTemplateService {
        return self.application.notificationTemplateService
    }
}
