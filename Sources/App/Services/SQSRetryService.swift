//
//  SQSRetryService.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent
import SotoSQS

// MARK: - SQS Retry Service
final class SQSRetryService {
    private let sqsClient: SQS
    private let logger: Logger
    private let retryQueueUrl: String
    private let dlqUrl: String
    
    init(awsClient: AWSClient, logger: Logger) {
        self.sqsClient = SQS(client: awsClient, region: .useast1)
        self.logger = logger
        self.retryQueueUrl = Environment.get("NOTIFICATION_RETRY_QUEUE_URL") ?? ""
        self.dlqUrl = Environment.get("NOTIFICATION_DLQ_URL") ?? ""
    }
    
    // MARK: - Send to Retry Queue
    func sendToRetryQueue(
        scheduleId: String,
        entityType: String,
        entityId: String,
        userId: String,
        reminderHoursBefore: Int,
        originalError: String,
        retryAttempt: Int = 1,
        maxRetries: Int = 3
    ) -> EventLoopFuture<Void> {
        let retryEvent = SQSRetryEvent(
            scheduleId: scheduleId,
            entityType: entityType,
            entityId: entityId,
            userId: userId,
            reminderHoursBefore: reminderHoursBefore,
            originalError: originalError,
            retryAttempt: retryAttempt,
            maxRetries: maxRetries,
            delaySeconds: calculateBackoffDelay(attempt: retryAttempt),
            timestamp: ISO8601DateFormatter().string(from: Date())
        )
        
        return sendRetryEvent(retryEvent)
    }
    
    // MARK: - Send Retry Event
    private func sendRetryEvent(_ event: SQSRetryEvent) -> EventLoopFuture<Void> {
        do {
            let messageBody = try JSONEncoder().encode(event)
            
            let sendMessageInput = SQS.SendMessageRequest(
                delaySeconds: event.delaySeconds,
                messageAttributes: [
                    "RetryAttempt": SQS.MessageAttributeValue(
                        dataType: "Number",
                        stringValue: "\(event.retryAttempt)"
                    ),
                    "ScheduleId": SQS.MessageAttributeValue(
                        dataType: "String",
                        stringValue: event.scheduleId
                    ),
                    "EntityType": SQS.MessageAttributeValue(
                        dataType: "String",
                        stringValue: event.entityType
                    )
                ],
                messageBody: String(data: messageBody, encoding: .utf8) ?? "",
                queueUrl: retryQueueUrl
            )
            
            logger.info("Sending notification \(event.scheduleId) to retry queue (attempt \(event.retryAttempt))")
            
            return sqsClient.sendMessage(sendMessageInput)
                .map { response in
                    self.logger.info("Successfully queued retry for schedule \(event.scheduleId), messageId: \(response.messageId ?? "unknown")")
                }
                .recover { error in
                    self.logger.error("Failed to send retry message for schedule \(event.scheduleId): \(error)")
                    throw error
                }
            
        } catch {
            logger.error("Failed to encode retry event for schedule \(event.scheduleId): \(error)")
            return sqsClient.eventLoopGroup.next().makeFailedFuture(error)
        }
    }
    
    // MARK: - Get Retry Queue Metrics
    func getRetryQueueMetrics() -> EventLoopFuture<QueueMetrics> {
        let getAttributesInput = SQS.GetQueueAttributesRequest(
            attributeNames: [.approximateNumberOfMessages, .approximateNumberOfMessagesNotVisible],
            queueUrl: retryQueueUrl
        )
        
        return sqsClient.getQueueAttributes(getAttributesInput)
            .map { response in
                let visibleMessages = Int(response.attributes?[.approximateNumberOfMessages] ?? "0") ?? 0
                let inFlightMessages = Int(response.attributes?[.approximateNumberOfMessagesNotVisible] ?? "0") ?? 0
                
                return QueueMetrics(
                    queueName: "retry-queue",
                    visibleMessages: visibleMessages,
                    inFlightMessages: inFlightMessages,
                    totalMessages: visibleMessages + inFlightMessages
                )
            }
    }
    
    // MARK: - Get DLQ Metrics
    func getDLQMetrics() -> EventLoopFuture<QueueMetrics> {
        let getAttributesInput = SQS.GetQueueAttributesRequest(
            attributeNames: [.approximateNumberOfMessages, .approximateNumberOfMessagesNotVisible],
            queueUrl: dlqUrl
        )
        
        return sqsClient.getQueueAttributes(getAttributesInput)
            .map { response in
                let visibleMessages = Int(response.attributes?[.approximateNumberOfMessages] ?? "0") ?? 0
                let inFlightMessages = Int(response.attributes?[.approximateNumberOfMessagesNotVisible] ?? "0") ?? 0
                
                return QueueMetrics(
                    queueName: "dead-letter-queue",
                    visibleMessages: visibleMessages,
                    inFlightMessages: inFlightMessages,
                    totalMessages: visibleMessages + inFlightMessages
                )
            }
    }
    
    // MARK: - Purge Retry Queue
    func purgeRetryQueue() -> EventLoopFuture<Void> {
        logger.warning("Purging retry queue - this will delete all pending retry messages")
        
        let purgeInput = SQS.PurgeQueueRequest(queueUrl: retryQueueUrl)
        
        return sqsClient.purgeQueue(purgeInput)
            .map { _ in
                self.logger.info("Successfully purged retry queue")
            }
    }
    
    // MARK: - Reprocess DLQ Messages
    func reprocessDLQMessages(maxMessages: Int = 10) -> EventLoopFuture<ReprocessResult> {
        logger.info("Reprocessing up to \(maxMessages) messages from DLQ")
        
        let receiveInput = SQS.ReceiveMessageRequest(
            maxNumberOfMessages: maxMessages,
            queueUrl: dlqUrl,
            waitTimeSeconds: 1
        )
        
        return sqsClient.receiveMessage(receiveInput)
            .flatMap { response in
                let messages = response.messages ?? []
                
                if messages.isEmpty {
                    return self.sqsClient.eventLoopGroup.next().makeSucceededFuture(
                        ReprocessResult(processed: 0, failed: 0, total: 0)
                    )
                }
                
                let reprocessFutures = messages.map { message in
                    self.reprocessSingleMessage(message)
                }
                
                return EventLoopFuture.whenAllComplete(reprocessFutures, on: self.sqsClient.eventLoopGroup.next())
                    .map { results in
                        let successful = results.filter { result in
                            if case .success = result { return true }
                            return false
                        }.count
                        
                        let failed = results.count - successful
                        
                        return ReprocessResult(
                            processed: successful,
                            failed: failed,
                            total: results.count
                        )
                    }
            }
    }
    
    // MARK: - Private Helper Methods
    private func reprocessSingleMessage(_ message: SQS.Message) -> EventLoopFuture<Void> {
        guard let body = message.body,
              let receiptHandle = message.receiptHandle else {
            return sqsClient.eventLoopGroup.next().makeFailedFuture(SQSError.invalidMessage)
        }
        
        do {
            let retryEvent = try JSONDecoder().decode(SQSRetryEvent.self, from: Data(body.utf8))
            
            // Reset retry attempt to 1 for reprocessing
            let reprocessEvent = SQSRetryEvent(
                scheduleId: retryEvent.scheduleId,
                entityType: retryEvent.entityType,
                entityId: retryEvent.entityId,
                userId: retryEvent.userId,
                reminderHoursBefore: retryEvent.reminderHoursBefore,
                originalError: "Reprocessed from DLQ: \(retryEvent.originalError)",
                retryAttempt: 1,
                maxRetries: retryEvent.maxRetries,
                delaySeconds: 0,
                timestamp: ISO8601DateFormatter().string(from: Date())
            )
            
            return sendRetryEvent(reprocessEvent)
                .flatMap { _ in
                    // Delete the message from DLQ after successful reprocessing
                    let deleteInput = SQS.DeleteMessageRequest(
                        queueUrl: self.dlqUrl,
                        receiptHandle: receiptHandle
                    )
                    
                    return self.sqsClient.deleteMessage(deleteInput)
                        .map { _ in
                            self.logger.info("Successfully reprocessed and deleted DLQ message for schedule \(retryEvent.scheduleId)")
                        }
                }
            
        } catch {
            logger.error("Failed to decode DLQ message: \(error)")
            return sqsClient.eventLoopGroup.next().makeFailedFuture(error)
        }
    }
    
    private func calculateBackoffDelay(attempt: Int) -> Int {
        // Exponential backoff: 2^attempt * 60 seconds, capped at 1 hour
        let delay = min(Int(pow(2.0, Double(attempt))) * 60, 3600)
        return delay
    }
}

// MARK: - Data Structures
struct SQSRetryEvent: Codable {
    let scheduleId: String
    let entityType: String
    let entityId: String
    let userId: String
    let reminderHoursBefore: Int
    let originalError: String
    let retryAttempt: Int
    let maxRetries: Int
    let delaySeconds: Int
    let timestamp: String
}

struct QueueMetrics: Content {
    let queueName: String
    let visibleMessages: Int
    let inFlightMessages: Int
    let totalMessages: Int
}

struct ReprocessResult: Content {
    let processed: Int
    let failed: Int
    let total: Int
    
    var successRate: Double {
        guard total > 0 else { return 0.0 }
        return Double(processed) / Double(total)
    }
}

// MARK: - Error Types
enum SQSError: Error, LocalizedError {
    case invalidMessage
    case queueNotConfigured
    case encodingFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidMessage:
            return "Invalid SQS message format"
        case .queueNotConfigured:
            return "SQS queue URLs not configured"
        case .encodingFailed:
            return "Failed to encode message for SQS"
        }
    }
}

// MARK: - Application Extension
extension Application {
    var sqsRetryService: SQSRetryService {
        return SQSRetryService(awsClient: self.aws.client, logger: self.logger)
    }
}

extension Request {
    var sqsRetryService: SQSRetryService {
        return self.application.sqsRetryService
    }
}
