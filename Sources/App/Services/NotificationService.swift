//
//  NotificationService.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent
import SotoEventBridge
import SotoSQS
import SotoSNS

// MARK: - Notification Service Protocol
protocol NotificationServiceProtocol {
    func scheduleReminders(for entity: NotifiableEntity, on db: Database, eventLoop: EventLoop) -> EventLoopFuture<Void>
    func cancelReminders(for entityType: EntityType, entityID: UUID, on db: Database, eventLoop: EventLoop) -> EventLoopFuture<Void>
    func sendNotification(_ notification: NotificationPayload, via channels: [DeliveryChannel], on req: Request) -> EventLoopFuture<Void>
}

// MARK: - Notifiable Entity Protocol
protocol NotifiableEntity {
    var id: UUID? { get }
    var title: String { get }
    var dueDate: Date { get }
    var entityType: EntityType { get }
    var assignedUserIDs: [UUID] { get }
}

// MARK: - Notification Payload
struct NotificationPayload: Content {
    let title: String
    let message: String
    let entityType: EntityType
    let entityID: UUID
    let userID: UUID
    let dueDate: Date
    let reminderHoursBefore: Int
    let metadata: [String: String]?
    
    init(title: String, message: String, entityType: EntityType, entityID: UUID, userID: UUID, dueDate: Date, reminderHoursBefore: Int, metadata: [String: String]? = nil) {
        self.title = title
        self.message = message
        self.entityType = entityType
        self.entityID = entityID
        self.userID = userID
        self.dueDate = dueDate
        self.reminderHoursBefore = reminderHoursBefore
        self.metadata = metadata
    }
}

// MARK: - Main Notification Service
final class NotificationService: NotificationServiceProtocol {
    private let eventBridgeClient: EventBridge
    private let sqsClient: SQS
    private let snsClient: SNS
    
    init(awsClient: AWSClient) {
        self.eventBridgeClient = EventBridge(client: awsClient, region: .useast1)
        self.sqsClient = SQS(client: awsClient, region: .useast1)
        self.snsClient = SNS(client: awsClient, region: .useast1)
    }
    
    // MARK: - Schedule Reminders
    func scheduleReminders(for entity: NotifiableEntity, on db: Database, eventLoop: EventLoop) -> EventLoopFuture<Void> {
        guard let entityID = entity.id else {
            return eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Entity ID is required"))
        }
        
        // First, cancel any existing reminders for this entity
        return cancelReminders(for: entity.entityType, entityID: entityID, on: db, eventLoop: eventLoop)
            .flatMap { _ in
                // Get all users who should receive notifications for this entity
                let userFutures = entity.assignedUserIDs.map { userID in
                    self.getUserNotificationPreferences(userID: userID, on: db)
                        .flatMap { preferences in
                            self.scheduleUserReminders(
                                entity: entity,
                                userID: userID,
                                preferences: preferences,
                                on: db,
                                eventLoop: eventLoop
                            )
                        }
                }
                
                return EventLoopFuture.andAllSucceed(userFutures, on: eventLoop)
            }
    }
    
    // MARK: - Cancel Reminders
    func cancelReminders(for entityType: EntityType, entityID: UUID, on db: Database, eventLoop: EventLoop) -> EventLoopFuture<Void> {
        return NotificationSchedule.query(on: db)
            .filter(\.$entityType == entityType.rawValue)
            .filter(\.$entityID == entityID)
            .filter(\.$status == ScheduleStatus.scheduled.rawValue)
            .all()
            .flatMap { schedules in
                let cancelFutures = schedules.map { schedule in
                    self.cancelEventBridgeSchedule(schedule: schedule, on: eventLoop)
                        .flatMap { _ in
                            schedule.status = ScheduleStatus.cancelled.rawValue
                            return schedule.update(on: db)
                        }
                }
                
                return EventLoopFuture.andAllSucceed(cancelFutures, on: eventLoop)
            }
    }
    
    // MARK: - Send Notification
    func sendNotification(_ notification: NotificationPayload, via channels: [DeliveryChannel], on req: Request) -> EventLoopFuture<Void> {
        return req.notificationDelivery.deliverNotification(notification, via: channels, on: req)
            .map { results in
                // Log delivery results
                for result in results {
                    if result.success {
                        req.logger.info("Successfully delivered notification via \(result.channel.rawValue)")
                    } else {
                        req.logger.error("Failed to deliver notification via \(result.channel.rawValue): \(result.error ?? "Unknown error")")
                    }
                }
            }
    }
    
    // MARK: - Private Helper Methods
    private func getUserNotificationPreferences(userID: UUID, on db: Database) -> EventLoopFuture<UserNotificationPreferences> {
        return UserNotificationPreferences.query(on: db)
            .filter(\.$user.$id == userID)
            .first()
            .unwrap(or: Abort(.notFound, reason: "User notification preferences not found"))
    }
    
    private func scheduleUserReminders(entity: NotifiableEntity, userID: UUID, preferences: UserNotificationPreferences, on db: Database, eventLoop: EventLoop) -> EventLoopFuture<Void> {
        let reminderFutures = preferences.reminderHours.map { hours in
            self.scheduleReminder(
                entity: entity,
                userID: userID,
                reminderHoursBefore: hours,
                on: db,
                eventLoop: eventLoop
            )
        }
        
        return EventLoopFuture.andAllSucceed(reminderFutures, on: eventLoop)
    }
    
    private func scheduleReminder(entity: NotifiableEntity, userID: UUID, reminderHoursBefore: Int, on db: Database, eventLoop: EventLoop) -> EventLoopFuture<Void> {
        guard let entityID = entity.id else {
            return eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Entity ID is required"))
        }
        
        let scheduledTime = entity.dueDate.addingTimeInterval(-Double(reminderHoursBefore * 3600))
        
        // Don't schedule reminders for past dates
        guard scheduledTime > Date() else {
            return eventLoop.makeSucceededFuture(())
        }
        
        let schedule = NotificationSchedule(
            userID: userID,
            entityType: entity.entityType.rawValue,
            entityID: entityID,
            entityTitle: entity.title,
            dueDate: entity.dueDate,
            reminderHoursBefore: reminderHoursBefore,
            scheduledTime: scheduledTime
        )
        
        return schedule.save(on: db)
            .flatMap { _ in
                self.createEventBridgeSchedule(for: schedule, on: eventLoop)
                    .flatMap { (scheduleName, scheduleArn) in
                        schedule.eventBridgeScheduleName = scheduleName
                        schedule.eventBridgeScheduleArn = scheduleArn
                        return schedule.update(on: db)
                    }
            }
    }
    
    private func createEventBridgeSchedule(for schedule: NotificationSchedule, on eventLoop: EventLoop) -> EventLoopFuture<(String, String)> {
        guard let scheduleID = schedule.id else {
            return eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Schedule ID is required"))
        }
        
        let scheduleName = "notification-\(scheduleID.uuidString)"
        let scheduleExpression = "at(\(ISO8601DateFormatter().string(from: schedule.scheduledTime)))"
        
        // Create EventBridge schedule input
        let target = EventBridge.Target(
            arn: Environment.get("NOTIFICATION_LAMBDA_ARN") ?? "",
            input: try! JSONEncoder().encode(schedule).base64EncodedString(),
            roleArn: Environment.get("EVENTBRIDGE_ROLE_ARN") ?? ""
        )
        
        let scheduleInput = EventBridge.CreateScheduleInput(
            flexibleTimeWindow: EventBridge.FlexibleTimeWindow(mode: .off),
            name: scheduleName,
            scheduleExpression: scheduleExpression,
            target: target
        )
        
        return eventBridgeClient.createSchedule(scheduleInput)
            .map { response in
                return (scheduleName, response.scheduleArn)
            }
    }
    
    private func cancelEventBridgeSchedule(schedule: NotificationSchedule, on eventLoop: EventLoop) -> EventLoopFuture<Void> {
        guard let scheduleName = schedule.eventBridgeScheduleName else {
            return eventLoop.makeSucceededFuture(())
        }
        
        let deleteInput = EventBridge.DeleteScheduleInput(name: scheduleName)
        
        return eventBridgeClient.deleteSchedule(deleteInput)
            .map { _ in () }
            .recover { error in
                // Log error but don't fail the operation
                print("Failed to delete EventBridge schedule: \(error)")
                return ()
            }
    }
    

}

// MARK: - Application Extension
extension Application {
    var notificationService: NotificationService {
        return NotificationService(awsClient: self.aws.client)
    }
}

extension Request {
    var notificationService: NotificationService {
        return self.application.notificationService
    }
}
