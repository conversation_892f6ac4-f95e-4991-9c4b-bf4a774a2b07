//
//  NotificationSchedulerService.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent
import SotoEventBridge

// MARK: - Notification Scheduler Service
final class NotificationSchedulerService {
    private let eventBridgeClient: EventBridge
    private let logger: Logger
    
    init(awsClient: AWSClient, logger: Logger) {
        self.eventBridgeClient = EventBridge(client: awsClient, region: .useast1)
        self.logger = logger
    }
    
    // MARK: - Schedule Entity Notifications
    func scheduleNotifications<T: NotifiableEntity>(for entity: T, on req: Request) -> EventLoopFuture<Void> {
        guard let entityID = entity.id else {
            logger.error("Cannot schedule notifications for entity without ID")
            return req.eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Entity ID is required"))
        }
        
        logger.info("Scheduling notifications for \(entity.entityType.rawValue) \(entityID)")
        
        // First cancel any existing schedules for this entity
        return cancelSchedules(for: entity.entityType, entityID: entityID, on: req)
            .flatMap { _ in
                // Get all users who should receive notifications
                return self.getNotificationRecipients(for: entity, on: req.db)
                    .flatMap { users in
                        let scheduleFutures = users.map { user in
                            self.scheduleUserNotifications(for: entity, user: user, on: req)
                        }
                        
                        return EventLoopFuture.andAllSucceed(scheduleFutures, on: req.eventLoop)
                    }
            }
    }
    
    // MARK: - Cancel Entity Notifications
    func cancelSchedules(for entityType: EntityType, entityID: UUID, on req: Request) -> EventLoopFuture<Void> {
        logger.info("Cancelling notifications for \(entityType.rawValue) \(entityID)")
        
        return NotificationSchedule.query(on: req.db)
            .filter(\.$entityType == entityType.rawValue)
            .filter(\.$entityID == entityID)
            .filter(\.$status == ScheduleStatus.scheduled.rawValue)
            .all()
            .flatMap { schedules in
                let cancelFutures = schedules.map { schedule in
                    self.cancelEventBridgeSchedule(schedule, on: req.eventLoop)
                        .flatMap { _ in
                            schedule.status = ScheduleStatus.cancelled.rawValue
                            return schedule.update(on: req.db)
                        }
                }
                
                return EventLoopFuture.andAllSucceed(cancelFutures, on: req.eventLoop)
            }
    }
    
    // MARK: - Process Scheduled Notification
    func processScheduledNotification(scheduleID: UUID, on req: Request) -> EventLoopFuture<Void> {
        logger.info("Processing scheduled notification \(scheduleID)")
        
        return NotificationSchedule.find(scheduleID, on: req.db)
            .unwrap(or: Abort(.notFound, reason: "Notification schedule not found"))
            .flatMap { schedule in
                // Load user preferences
                return UserNotificationPreferences.query(on: req.db)
                    .filter(\.$user.$id == schedule.$user.id)
                    .first()
                    .unwrap(or: Abort(.notFound, reason: "User notification preferences not found"))
                    .flatMap { preferences in
                        // Create notification payload
                        let payload = NotificationPayload(
                            title: self.generateNotificationTitle(for: schedule),
                            message: self.generateNotificationMessage(for: schedule),
                            entityType: EntityType(rawValue: schedule.entityType) ?? .task,
                            entityID: schedule.entityID,
                            userID: try! schedule.$user.id,
                            dueDate: schedule.dueDate,
                            reminderHoursBefore: schedule.reminderHoursBefore,
                            metadata: [
                                "schedule_id": scheduleID.uuidString,
                                "entity_title": schedule.entityTitle
                            ]
                        )
                        
                        // Determine delivery channels based on preferences
                        let channels = self.getEnabledChannels(from: preferences)
                        
                        // Send notifications
                        return req.notificationService.sendNotification(payload, via: channels, on: req)
                            .flatMap { _ in
                                // Mark schedule as sent
                                schedule.status = ScheduleStatus.sent.rawValue
                                return schedule.update(on: req.db)
                            }
                    }
            }
            .recover { error in
                self.logger.error("Failed to process scheduled notification \(scheduleID): \(error)")
                
                // Mark schedule as failed
                return NotificationSchedule.find(scheduleID, on: req.db)
                    .flatMap { schedule in
                        if let schedule = schedule {
                            schedule.status = ScheduleStatus.failed.rawValue
                            schedule.lastError = error.localizedDescription
                            schedule.deliveryAttempts = (schedule.deliveryAttempts ?? 0) + 1
                            return schedule.update(on: req.db)
                        }
                        return req.eventLoop.makeSucceededFuture(())
                    }
            }
    }
    
    // MARK: - Private Helper Methods
    private func getNotificationRecipients<T: NotifiableEntity>(for entity: T, on db: Database) -> EventLoopFuture<[User]> {
        let userIDs = entity.assignedUserIDs
        
        if userIDs.isEmpty {
            return db.eventLoop.makeSucceededFuture([])
        }
        
        return User.query(on: db)
            .filter(\.$id ~~ userIDs)
            .all()
    }
    
    private func scheduleUserNotifications<T: NotifiableEntity>(for entity: T, user: User, on req: Request) -> EventLoopFuture<Void> {
        guard let userID = user.id, let entityID = entity.id else {
            return req.eventLoop.makeSucceededFuture(())
        }
        
        // Get user's notification preferences
        return UserNotificationPreferences.query(on: req.db)
            .filter(\.$user.$id == userID)
            .first()
            .flatMap { preferences in
                let reminderHours = preferences?.reminderHours ?? [24, 48]
                
                let scheduleFutures = reminderHours.map { hours in
                    self.createNotificationSchedule(
                        entity: entity,
                        userID: userID,
                        reminderHoursBefore: hours,
                        on: req
                    )
                }
                
                return EventLoopFuture.andAllSucceed(scheduleFutures, on: req.eventLoop)
            }
    }
    
    private func createNotificationSchedule<T: NotifiableEntity>(entity: T, userID: UUID, reminderHoursBefore: Int, on req: Request) -> EventLoopFuture<Void> {
        guard let entityID = entity.id else {
            return req.eventLoop.makeSucceededFuture(())
        }
        
        let scheduledTime = entity.dueDate.addingTimeInterval(-Double(reminderHoursBefore * 3600))
        
        // Don't schedule notifications for past dates
        guard scheduledTime > Date() else {
            logger.info("Skipping notification schedule for past date: \(scheduledTime)")
            return req.eventLoop.makeSucceededFuture(())
        }
        
        let schedule = NotificationSchedule(
            userID: userID,
            entityType: entity.entityType.rawValue,
            entityID: entityID,
            entityTitle: entity.title,
            dueDate: entity.dueDate,
            reminderHoursBefore: reminderHoursBefore,
            scheduledTime: scheduledTime
        )
        
        return schedule.save(on: req.db)
            .flatMap { _ in
                self.createEventBridgeSchedule(for: schedule, on: req.eventLoop)
                    .flatMap { (scheduleName, scheduleArn) in
                        schedule.eventBridgeScheduleName = scheduleName
                        schedule.eventBridgeScheduleArn = scheduleArn
                        return schedule.update(on: req.db)
                    }
                    .map { _ in
                        // Record schedule creation metric
                        _ = req.notificationMonitoring.recordScheduleCreated(
                            entityType: entity.entityType,
                            reminderHours: reminderHoursBefore
                        )
                    }
            }
    }
    
    private func createEventBridgeSchedule(for schedule: NotificationSchedule, on eventLoop: EventLoop) -> EventLoopFuture<(String, String)> {
        guard let scheduleID = schedule.id else {
            return eventLoop.makeFailedFuture(Abort(.badRequest, reason: "Schedule ID is required"))
        }
        
        let scheduleName = "notification-\(scheduleID.uuidString)"
        
        // Format the scheduled time for EventBridge
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        let scheduleExpression = "at(\(formatter.string(from: schedule.scheduledTime)))"
        
        // Create the Lambda target
        let lambdaArn = Environment.get("NOTIFICATION_LAMBDA_ARN") ?? "arn:aws:lambda:us-east-1:123456789012:function:process-notification"
        let roleArn = Environment.get("EVENTBRIDGE_ROLE_ARN") ?? "arn:aws:iam::123456789012:role/EventBridgeRole"
        
        let inputPayload = [
            "scheduleId": scheduleID.uuidString,
            "entityType": schedule.entityType,
            "entityId": schedule.entityID.uuidString,
            "userId": try! schedule.$user.id.uuidString,
            "reminderHoursBefore": schedule.reminderHoursBefore
        ]
        
        let target = EventBridge.Target(
            arn: lambdaArn,
            input: try! JSONSerialization.data(withJSONObject: inputPayload).base64EncodedString(),
            roleArn: roleArn
        )
        
        let scheduleInput = EventBridge.CreateScheduleInput(
            flexibleTimeWindow: EventBridge.FlexibleTimeWindow(mode: .off),
            name: scheduleName,
            scheduleExpression: scheduleExpression,
            target: target
        )
        
        logger.info("Creating EventBridge schedule: \(scheduleName) for \(scheduleExpression)")
        
        return eventBridgeClient.createSchedule(scheduleInput)
            .map { response in
                self.logger.info("Created EventBridge schedule: \(response.scheduleArn)")
                return (scheduleName, response.scheduleArn)
            }
            .recover { error in
                self.logger.error("Failed to create EventBridge schedule: \(error)")
                throw error
            }
    }
    
    private func cancelEventBridgeSchedule(_ schedule: NotificationSchedule, on eventLoop: EventLoop) -> EventLoopFuture<Void> {
        guard let scheduleName = schedule.eventBridgeScheduleName else {
            return eventLoop.makeSucceededFuture(())
        }
        
        let deleteInput = EventBridge.DeleteScheduleInput(name: scheduleName)
        
        return eventBridgeClient.deleteSchedule(deleteInput)
            .map { _ in
                self.logger.info("Deleted EventBridge schedule: \(scheduleName)")
            }
            .recover { error in
                self.logger.warning("Failed to delete EventBridge schedule \(scheduleName): \(error)")
                // Don't fail the operation if we can't delete the schedule
            }
    }
    
    private func generateNotificationTitle(for schedule: NotificationSchedule) -> String {
        let entityType = schedule.entityType.capitalized
        let timeDescription = schedule.reminderHoursBefore == 24 ? "tomorrow" : "in \(schedule.reminderHoursBefore) hours"
        
        return "\(entityType) Due \(timeDescription.capitalized)"
    }
    
    private func generateNotificationMessage(for schedule: NotificationSchedule) -> String {
        let timeDescription = schedule.reminderHoursBefore == 24 ? "tomorrow" : "in \(schedule.reminderHoursBefore) hours"
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        
        return "Your \(schedule.entityType) '\(schedule.entityTitle)' is due \(timeDescription) (\(formatter.string(from: schedule.dueDate)))."
    }
    
    private func getEnabledChannels(from preferences: UserNotificationPreferences) -> [DeliveryChannel] {
        var channels: [DeliveryChannel] = []
        
        if preferences.smsEnabled && preferences.smsPhoneNumber != nil {
            channels.append(.sms)
        }
        
        if preferences.emailEnabled && preferences.emailAddress != nil {
            channels.append(.email)
        }
        
        if preferences.pushEnabled {
            channels.append(.push)
        }
        
        if preferences.browserEnabled {
            channels.append(.browser)
        }
        
        if preferences.inAppEnabled {
            channels.append(.inApp)
        }
        
        return channels
    }
}

// MARK: - Application Extension
extension Application {
    var notificationScheduler: NotificationSchedulerService {
        return NotificationSchedulerService(awsClient: self.aws.client, logger: self.logger)
    }
}

extension Request {
    var notificationScheduler: NotificationSchedulerService {
        return self.application.notificationScheduler
    }
}
