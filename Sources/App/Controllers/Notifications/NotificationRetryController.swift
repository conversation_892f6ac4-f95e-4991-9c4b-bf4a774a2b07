//
//  NotificationRetryController.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent

// MARK: - Request/Response DTOs
struct RetryQueueStatusResponse: Content {
    let retryQueue: QueueMetrics
    let deadLetterQueue: QueueMetrics
    let timestamp: Date
}

struct ReprocessRequest: Content {
    let maxMessages: Int?
    let reason: String?
}

struct PurgeRequest: Content {
    let queueType: String // "retry" or "dlq"
    let confirmation: String // Must be "CONFIRM_PURGE"
    let reason: String
}

struct NotificationScheduleStatusResponse: Content {
    let scheduleId: UUID
    let entityType: String
    let entityId: UUID
    let userId: UUID
    let status: String
    let deliveryAttempts: Int?
    let lastError: String?
    let scheduledTime: Date
    let dueDate: Date
    let createdAt: Date?
    let updatedAt: Date?
}

// MARK: - Notification Retry Controller
struct NotificationRetryController: RouteCollection {
    
    func boot(routes: Route<PERSON><PERSON><PERSON><PERSON>) throws {
        let retry = routes.grouped("notification-retry")
        
        // Queue monitoring
        retry.get("status", use: getQueueStatus)
        retry.get("metrics", use: getDetailedMetrics)
        
        // Queue management
        retry.post("reprocess", use: reprocessDLQMessages)
        retry.post("purge", use: purgeQueue)
        
        // Schedule monitoring
        retry.get("schedules", use: getNotificationSchedules)
        retry.get("schedules", ":scheduleID", use: getNotificationSchedule)
        retry.put("schedules", ":scheduleID", "retry", use: retryNotificationSchedule)
        retry.delete("schedules", ":scheduleID", use: cancelNotificationSchedule)
        
        // Failed notifications
        retry.get("failed", use: getFailedNotifications)
        retry.post("failed", ":scheduleID", "retry", use: retryFailedNotification)
    }
    
    // MARK: - Queue Status
    func getQueueStatus(req: Request) throws -> EventLoopFuture<RetryQueueStatusResponse> {
        let retryMetricsFuture = req.sqsRetryService.getRetryQueueMetrics()
        let dlqMetricsFuture = req.sqsRetryService.getDLQMetrics()
        
        return retryMetricsFuture.and(dlqMetricsFuture)
            .map { (retryMetrics, dlqMetrics) in
                return RetryQueueStatusResponse(
                    retryQueue: retryMetrics,
                    deadLetterQueue: dlqMetrics,
                    timestamp: Date()
                )
            }
    }
    
    // MARK: - Detailed Metrics
    func getDetailedMetrics(req: Request) throws -> EventLoopFuture<[String: Any]> {
        let retryMetricsFuture = req.sqsRetryService.getRetryQueueMetrics()
        let dlqMetricsFuture = req.sqsRetryService.getDLQMetrics()
        
        // Get database metrics
        let scheduledCountFuture = NotificationSchedule.query(on: req.db)
            .filter(\.$status == ScheduleStatus.scheduled.rawValue)
            .count()
        
        let sentCountFuture = NotificationSchedule.query(on: req.db)
            .filter(\.$status == ScheduleStatus.sent.rawValue)
            .count()
        
        let failedCountFuture = NotificationSchedule.query(on: req.db)
            .filter(\.$status == ScheduleStatus.failed.rawValue)
            .count()
        
        return retryMetricsFuture.and(dlqMetricsFuture)
            .and(scheduledCountFuture)
            .and(sentCountFuture)
            .and(failedCountFuture)
            .map { ((retryMetrics, dlqMetrics), scheduledCount, sentCount, failedCount) in
                return [
                    "queues": [
                        "retry": [
                            "visible_messages": retryMetrics.visibleMessages,
                            "in_flight_messages": retryMetrics.inFlightMessages,
                            "total_messages": retryMetrics.totalMessages
                        ],
                        "dead_letter": [
                            "visible_messages": dlqMetrics.visibleMessages,
                            "in_flight_messages": dlqMetrics.inFlightMessages,
                            "total_messages": dlqMetrics.totalMessages
                        ]
                    ],
                    "schedules": [
                        "scheduled": scheduledCount,
                        "sent": sentCount,
                        "failed": failedCount,
                        "total": scheduledCount + sentCount + failedCount
                    ],
                    "timestamp": ISO8601DateFormatter().string(from: Date())
                ]
            }
    }
    
    // MARK: - Reprocess DLQ Messages
    func reprocessDLQMessages(req: Request) throws -> EventLoopFuture<ReprocessResult> {
        let input = try req.content.decode(ReprocessRequest.self)
        let maxMessages = input.maxMessages ?? 10
        
        req.logger.info("Reprocessing DLQ messages (max: \(maxMessages)), reason: \(input.reason ?? "Manual reprocess")")
        
        return req.sqsRetryService.reprocessDLQMessages(maxMessages: maxMessages)
    }
    
    // MARK: - Purge Queue
    func purgeQueue(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let input = try req.content.decode(PurgeRequest.self)
        
        guard input.confirmation == "CONFIRM_PURGE" else {
            throw Abort(.badRequest, reason: "Confirmation string must be 'CONFIRM_PURGE'")
        }
        
        req.logger.warning("Purging \(input.queueType) queue, reason: \(input.reason)")
        
        switch input.queueType.lowercased() {
        case "retry":
            return req.sqsRetryService.purgeRetryQueue()
                .transform(to: .noContent)
        default:
            throw Abort(.badRequest, reason: "Invalid queue type. Use 'retry'")
        }
    }
    
    // MARK: - Get Notification Schedules
    func getNotificationSchedules(req: Request) throws -> EventLoopFuture<Page<NotificationScheduleStatusResponse>> {
        let status: String? = req.query["status"]
        let entityType: String? = req.query["entityType"]
        let userID: String? = req.query["userID"]
        
        var query = NotificationSchedule.query(on: req.db)
        
        if let status = status {
            query = query.filter(\.$status == status)
        }
        
        if let entityType = entityType {
            query = query.filter(\.$entityType == entityType)
        }
        
        if let userIDString = userID, let uuid = UUID(uuidString: userIDString) {
            query = query.filter(\.$user.$id == uuid)
        }
        
        return query.sort(\.$createdAt, .descending)
            .paginate(for: req)
            .flatMapThrowing { page in
                return try page.map { schedule in
                    return try NotificationScheduleStatusResponse(
                        scheduleId: schedule.requireID(),
                        entityType: schedule.entityType,
                        entityId: schedule.entityID,
                        userId: schedule.$user.id,
                        status: schedule.status,
                        deliveryAttempts: schedule.deliveryAttempts,
                        lastError: schedule.lastError,
                        scheduledTime: schedule.scheduledTime,
                        dueDate: schedule.dueDate,
                        createdAt: schedule.createdAt,
                        updatedAt: schedule.updatedAt
                    )
                }
            }
    }
    
    // MARK: - Get Single Notification Schedule
    func getNotificationSchedule(req: Request) throws -> EventLoopFuture<NotificationScheduleStatusResponse> {
        let scheduleID = try req.parameters.require("scheduleID", as: UUID.self)
        
        return NotificationSchedule.find(scheduleID, on: req.db)
            .unwrap(or: Abort(.notFound, reason: "Notification schedule not found"))
            .flatMapThrowing { schedule in
                return try NotificationScheduleStatusResponse(
                    scheduleId: schedule.requireID(),
                    entityType: schedule.entityType,
                    entityId: schedule.entityID,
                    userId: schedule.$user.id,
                    status: schedule.status,
                    deliveryAttempts: schedule.deliveryAttempts,
                    lastError: schedule.lastError,
                    scheduledTime: schedule.scheduledTime,
                    dueDate: schedule.dueDate,
                    createdAt: schedule.createdAt,
                    updatedAt: schedule.updatedAt
                )
            }
    }
    
    // MARK: - Retry Notification Schedule
    func retryNotificationSchedule(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let scheduleID = try req.parameters.require("scheduleID", as: UUID.self)
        
        return NotificationSchedule.find(scheduleID, on: req.db)
            .unwrap(or: Abort(.notFound, reason: "Notification schedule not found"))
            .flatMap { schedule in
                // Send to retry queue
                return req.sqsRetryService.sendToRetryQueue(
                    scheduleId: scheduleID.uuidString,
                    entityType: schedule.entityType,
                    entityId: schedule.entityID.uuidString,
                    userId: try! schedule.$user.id.uuidString,
                    reminderHoursBefore: schedule.reminderHoursBefore,
                    originalError: "Manual retry requested"
                ).flatMap { _ in
                    // Update schedule status
                    schedule.status = ScheduleStatus.scheduled.rawValue
                    schedule.lastError = nil
                    return schedule.update(on: req.db)
                        .transform(to: .accepted)
                }
            }
    }
    
    // MARK: - Cancel Notification Schedule
    func cancelNotificationSchedule(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let scheduleID = try req.parameters.require("scheduleID", as: UUID.self)
        
        return NotificationSchedule.find(scheduleID, on: req.db)
            .unwrap(or: Abort(.notFound, reason: "Notification schedule not found"))
            .flatMap { schedule in
                schedule.status = ScheduleStatus.cancelled.rawValue
                return schedule.update(on: req.db)
                    .transform(to: .noContent)
            }
    }
    
    // MARK: - Get Failed Notifications
    func getFailedNotifications(req: Request) throws -> EventLoopFuture<Page<NotificationScheduleStatusResponse>> {
        return NotificationSchedule.query(on: req.db)
            .filter(\.$status == ScheduleStatus.failed.rawValue)
            .sort(\.$updatedAt, .descending)
            .paginate(for: req)
            .flatMapThrowing { page in
                return try page.map { schedule in
                    return try NotificationScheduleStatusResponse(
                        scheduleId: schedule.requireID(),
                        entityType: schedule.entityType,
                        entityId: schedule.entityID,
                        userId: schedule.$user.id,
                        status: schedule.status,
                        deliveryAttempts: schedule.deliveryAttempts,
                        lastError: schedule.lastError,
                        scheduledTime: schedule.scheduledTime,
                        dueDate: schedule.dueDate,
                        createdAt: schedule.createdAt,
                        updatedAt: schedule.updatedAt
                    )
                }
            }
    }
    
    // MARK: - Retry Failed Notification
    func retryFailedNotification(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let scheduleID = try req.parameters.require("scheduleID", as: UUID.self)
        
        return NotificationSchedule.find(scheduleID, on: req.db)
            .unwrap(or: Abort(.notFound, reason: "Notification schedule not found"))
            .flatMap { schedule in
                guard schedule.status == ScheduleStatus.failed.rawValue else {
                    return req.eventLoop.makeFailedFuture(
                        Abort(.badRequest, reason: "Only failed notifications can be retried")
                    )
                }
                
                // Send to retry queue with reset attempt count
                return req.sqsRetryService.sendToRetryQueue(
                    scheduleId: scheduleID.uuidString,
                    entityType: schedule.entityType,
                    entityId: schedule.entityID.uuidString,
                    userId: try! schedule.$user.id.uuidString,
                    reminderHoursBefore: schedule.reminderHoursBefore,
                    originalError: "Manual retry of failed notification",
                    retryAttempt: 1,
                    maxRetries: 3
                ).flatMap { _ in
                    // Update schedule status
                    schedule.status = ScheduleStatus.scheduled.rawValue
                    schedule.lastError = nil
                    schedule.deliveryAttempts = 0
                    return schedule.update(on: req.db)
                        .transform(to: .accepted)
                }
            }
    }
}
