//
//  NotificationTemplateController.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent

// MARK: - Request/Response DTOs
struct NotificationTemplateRequest: Content {
    let templateKey: String
    let entityType: String
    let deliveryChannel: String
    let subjectTemplate: String?
    let bodyTemplate: String
    let variables: [String]
    let isActive: Bool?
}

struct NotificationTemplateResponse: Content {
    let id: UUID?
    let templateKey: String
    let entityType: String
    let deliveryChannel: String
    let subjectTemplate: String?
    let bodyTemplate: String
    let variables: [String]
    let isActive: Bool
    let createdAt: Date?
    let updatedAt: Date?
    
    init(from template: NotificationTemplate) throws {
        self.id = template.id
        self.templateKey = template.templateKey
        self.entityType = template.entityType
        self.deliveryChannel = template.deliveryChannel
        self.subjectTemplate = template.subjectTemplate
        self.bodyTemplate = template.bodyTemplate
        self.variables = template.variables
        self.isActive = template.isActive
        self.createdAt = template.createdAt
        self.updatedAt = template.updatedAt
    }
}

struct TemplatePreviewRequest: Content {
    let templateId: UUID?
    let templateKey: String?
    let entityType: String
    let deliveryChannel: String
    let subjectTemplate: String?
    let bodyTemplate: String
    let variables: [String]
    let sampleData: TemplatePreviewData
}

struct TemplatePreviewData: Content {
    let entityTitle: String
    let dueDate: String
    let reminderHours: Int
    let userName: String?
    let userEmail: String?
}

struct TemplatePreviewResponse: Content {
    let subject: String?
    let body: String
    let variables: [String: String]
}

// MARK: - Notification Template Controller
struct NotificationTemplateController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let templates = routes.grouped("notification-templates")
        
        // CRUD operations
        templates.get(use: listTemplates)
        templates.post(use: createTemplate)
        templates.get(":templateID", use: getTemplate)
        templates.put(":templateID", use: updateTemplate)
        templates.delete(":templateID", use: deleteTemplate)
        
        // Template management
        templates.post("preview", use: previewTemplate)
        templates.post("initialize-defaults", use: initializeDefaults)
        templates.get("entity-types", use: getEntityTypes)
        templates.get("delivery-channels", use: getDeliveryChannels)
        templates.get("variables", use: getAvailableVariables)
    }
    
    // MARK: - List Templates
    func listTemplates(req: Request) throws -> EventLoopFuture<[NotificationTemplateResponse]> {
        let entityType: String? = req.query["entityType"]
        let deliveryChannel: String? = req.query["deliveryChannel"]
        let isActive: Bool? = req.query["isActive"]
        
        return req.notificationTemplateService.listTemplates(
            entityType: entityType,
            deliveryChannel: deliveryChannel,
            isActive: isActive,
            on: req.db
        ).flatMapThrowing { templates in
            return try templates.map { try NotificationTemplateResponse(from: $0) }
        }
    }
    
    // MARK: - Create Template
    func createTemplate(req: Request) throws -> EventLoopFuture<NotificationTemplateResponse> {
        let input = try req.content.decode(NotificationTemplateRequest.self)
        
        // Validate input
        let template = NotificationTemplate(
            templateKey: input.templateKey,
            entityType: input.entityType,
            deliveryChannel: input.deliveryChannel,
            subjectTemplate: input.subjectTemplate,
            bodyTemplate: input.bodyTemplate,
            variables: input.variables,
            isActive: input.isActive ?? true
        )
        
        let validationErrors = req.notificationTemplateService.validateTemplate(template)
        guard validationErrors.isEmpty else {
            throw Abort(.badRequest, reason: "Validation errors: \(validationErrors.joined(separator: ", "))")
        }
        
        return req.notificationTemplateService.createTemplate(
            templateKey: input.templateKey,
            entityType: input.entityType,
            deliveryChannel: input.deliveryChannel,
            subjectTemplate: input.subjectTemplate,
            bodyTemplate: input.bodyTemplate,
            variables: input.variables,
            on: req.db
        ).flatMapThrowing { template in
            return try NotificationTemplateResponse(from: template)
        }
    }
    
    // MARK: - Get Template
    func getTemplate(req: Request) throws -> EventLoopFuture<NotificationTemplateResponse> {
        let templateID = try req.parameters.require("templateID", as: UUID.self)
        
        return NotificationTemplate.find(templateID, on: req.db)
            .unwrap(or: Abort(.notFound, reason: "Template not found"))
            .flatMapThrowing { template in
                return try NotificationTemplateResponse(from: template)
            }
    }
    
    // MARK: - Update Template
    func updateTemplate(req: Request) throws -> EventLoopFuture<NotificationTemplateResponse> {
        let templateID = try req.parameters.require("templateID", as: UUID.self)
        let input = try req.content.decode(NotificationTemplateRequest.self)
        
        return req.notificationTemplateService.updateTemplate(
            id: templateID,
            subjectTemplate: input.subjectTemplate,
            bodyTemplate: input.bodyTemplate,
            variables: input.variables,
            isActive: input.isActive ?? true,
            on: req.db
        ).flatMapThrowing { template in
            return try NotificationTemplateResponse(from: template)
        }
    }
    
    // MARK: - Delete Template
    func deleteTemplate(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let templateID = try req.parameters.require("templateID", as: UUID.self)
        
        return req.notificationTemplateService.deleteTemplate(id: templateID, on: req.db)
            .transform(to: .noContent)
    }
    
    // MARK: - Preview Template
    func previewTemplate(req: Request) throws -> EventLoopFuture<TemplatePreviewResponse> {
        let input = try req.content.decode(TemplatePreviewRequest.self)
        
        // Create a temporary template for preview
        let template = NotificationTemplate(
            templateKey: input.templateKey ?? "preview",
            entityType: input.entityType,
            deliveryChannel: input.deliveryChannel,
            subjectTemplate: input.subjectTemplate,
            bodyTemplate: input.bodyTemplate,
            variables: input.variables
        )
        
        // Create template variables from sample data
        let dateFormatter = ISO8601DateFormatter()
        let dueDate = dateFormatter.date(from: input.sampleData.dueDate) ?? Date()
        
        let variables = TemplateVariables(
            entityTitle: input.sampleData.entityTitle,
            entityType: input.entityType,
            dueDate: dueDate,
            reminderHours: input.sampleData.reminderHours,
            userName: input.sampleData.userName,
            userEmail: input.sampleData.userEmail,
            appUrl: Environment.get("APP_URL") ?? "https://app.wellup.com",
            organizationName: Environment.get("ORGANIZATION_NAME") ?? "Wellup"
        )
        
        let rendered = NotificationTemplateRenderer.render(template: template, variables: variables)
        
        return req.eventLoop.makeSucceededFuture(
            TemplatePreviewResponse(
                subject: rendered.subject,
                body: rendered.body,
                variables: variables.toDictionary()
            )
        )
    }
    
    // MARK: - Initialize Default Templates
    func initializeDefaults(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        return req.notificationTemplateService.initializeDefaultTemplates(on: req.db)
            .transform(to: .created)
    }
    
    // MARK: - Get Entity Types
    func getEntityTypes(req: Request) throws -> [String: Any] {
        return [
            "entity_types": EntityType.allCases.map { $0.rawValue } + ["generic"],
            "descriptions": [
                "task": "Task reminders and notifications",
                "goal": "Goal deadline and progress notifications",
                "intervention": "Intervention due date reminders",
                "follow_up": "Follow-up appointment reminders",
                "generic": "Generic template for all entity types"
            ]
        ]
    }
    
    // MARK: - Get Delivery Channels
    func getDeliveryChannels(req: Request) throws -> [String: Any] {
        return [
            "delivery_channels": DeliveryChannel.allCases.map { $0.rawValue },
            "descriptions": [
                "sms": "Text message notifications",
                "email": "Email notifications",
                "push": "Mobile push notifications",
                "browser": "Browser push notifications",
                "in_app": "In-app notifications"
            ]
        ]
    }
    
    // MARK: - Get Available Variables
    func getAvailableVariables(req: Request) throws -> [String: Any] {
        return [
            "variables": [
                "entity_title": "Title of the entity (task, goal, etc.)",
                "entity_type": "Type of entity (task, goal, intervention, follow_up)",
                "entity_type_capitalized": "Capitalized entity type",
                "due_date": "Full formatted due date and time",
                "due_date_short": "Short formatted due date",
                "reminder_hours": "Number of hours before due date",
                "time_until_due": "Human readable time until due (e.g., 'tomorrow', 'in 48 hours')",
                "user_name": "User's first name",
                "user_email": "User's email address",
                "app_url": "Application URL",
                "organization_name": "Organization name"
            ],
            "usage": [
                "single_braces": "Use {variable_name} for single brace syntax",
                "double_braces": "Use {{variable_name}} for double brace syntax",
                "both_supported": "Both syntaxes are supported in templates"
            ],
            "examples": [
                "sms": "Reminder: Your {entity_type} '{entity_title}' is due {time_until_due}.",
                "email_subject": "{entity_type_capitalized} Reminder: {entity_title}",
                "email_body": "Hello {user_name}, your {entity_type} '{entity_title}' is due {time_until_due} ({due_date})."
            ]
        ]
    }
}
