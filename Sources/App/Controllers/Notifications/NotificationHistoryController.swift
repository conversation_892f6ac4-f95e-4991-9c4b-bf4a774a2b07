//
//  NotificationHistoryController.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent

// MARK: - Request/Response DTOs
struct NotificationHistoryResponse: Content {
    let id: UUID?
    let title: String
    let kind: String
    let message: String
    let read: Bool
    let entityType: String?
    let entityId: UUID?
    let deliveryChannel: String?
    let deliveryStatus: String?
    let scheduledFor: Date?
    let createdAt: Date?
    let updatedAt: Date?
    
    init(from notification: UserNotification) {
        self.id = notification.id
        self.title = notification.title
        self.kind = notification.kind
        self.message = notification.message
        self.read = notification.read
        self.entityType = notification.entityType
        self.entityId = notification.entityID
        self.deliveryChannel = notification.deliveryChannel
        self.deliveryStatus = notification.deliveryStatus
        self.scheduledFor = notification.scheduledFor
        self.createdAt = notification.createdAt
        self.updatedAt = notification.updatedAt
    }
}

struct NotificationStatsResponse: Content {
    let totalNotifications: Int
    let unreadNotifications: Int
    let readNotifications: Int
    let notificationsByKind: [String: Int]
    let notificationsByChannel: [String: Int]
    let notificationsByStatus: [String: Int]
    let recentActivity: [NotificationHistoryResponse]
}

struct MarkReadRequest: Content {
    let notificationIds: [UUID]?
    let markAll: Bool?
}

struct BulkDeleteRequest: Content {
    let notificationIds: [UUID]?
    let deleteAll: Bool?
    let olderThan: Date?
}

// MARK: - Notification History Controller
struct NotificationHistoryController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let notifications = routes.grouped("notifications")
        
        // History and management
        notifications.get(use: getNotifications)
        notifications.get("stats", use: getNotificationStats)
        notifications.get(":notificationID", use: getNotification)
        
        // Mark as read/unread
        notifications.put(":notificationID", "read", use: markAsRead)
        notifications.put(":notificationID", "unread", use: markAsUnread)
        notifications.post("mark-read", use: bulkMarkAsRead)
        
        // Delete notifications
        notifications.delete(":notificationID", use: deleteNotification)
        notifications.post("bulk-delete", use: bulkDeleteNotifications)
        
        // Export and search
        notifications.get("export", use: exportNotifications)
        notifications.get("search", use: searchNotifications)
    }
    
    // MARK: - Get Notifications
    func getNotifications(req: Request) throws -> EventLoopFuture<Page<NotificationHistoryResponse>> {
        let userID = try getUserID(from: req)
        
        // Query parameters
        let read: Bool? = req.query["read"]
        let kind: String? = req.query["kind"]
        let entityType: String? = req.query["entityType"]
        let deliveryChannel: String? = req.query["deliveryChannel"]
        let deliveryStatus: String? = req.query["deliveryStatus"]
        let since: Date? = req.query["since"]
        
        var query = UserNotification.query(on: req.db)
            .filter(\.$userID == userID)
        
        if let read = read {
            query = query.filter(\.$read == read)
        }
        
        if let kind = kind {
            query = query.filter(\.$kind == kind)
        }
        
        if let entityType = entityType {
            query = query.filter(\.$entityType == entityType)
        }
        
        if let deliveryChannel = deliveryChannel {
            query = query.filter(\.$deliveryChannel == deliveryChannel)
        }
        
        if let deliveryStatus = deliveryStatus {
            query = query.filter(\.$deliveryStatus == deliveryStatus)
        }
        
        if let since = since {
            query = query.filter(\.$createdAt >= since)
        }
        
        return query.sort(\.$createdAt, .descending)
            .paginate(for: req)
            .map { page in
                return page.map { notification in
                    return NotificationHistoryResponse(from: notification)
                }
            }
    }
    
    // MARK: - Get Notification Stats
    func getNotificationStats(req: Request) throws -> EventLoopFuture<NotificationStatsResponse> {
        let userID = try getUserID(from: req)
        
        let totalFuture = UserNotification.query(on: req.db)
            .filter(\.$userID == userID)
            .count()
        
        let unreadFuture = UserNotification.query(on: req.db)
            .filter(\.$userID == userID)
            .filter(\.$read == false)
            .count()
        
        let recentFuture = UserNotification.query(on: req.db)
            .filter(\.$userID == userID)
            .sort(\.$createdAt, .descending)
            .limit(10)
            .all()
        
        // Get stats by kind
        let kindStatsFuture = UserNotification.query(on: req.db)
            .filter(\.$userID == userID)
            .all()
            .map { notifications in
                var kindStats: [String: Int] = [:]
                for notification in notifications {
                    kindStats[notification.kind, default: 0] += 1
                }
                return kindStats
            }
        
        // Get stats by channel
        let channelStatsFuture = UserNotification.query(on: req.db)
            .filter(\.$userID == userID)
            .filter(\.$deliveryChannel != nil)
            .all()
            .map { notifications in
                var channelStats: [String: Int] = [:]
                for notification in notifications {
                    if let channel = notification.deliveryChannel {
                        channelStats[channel, default: 0] += 1
                    }
                }
                return channelStats
            }
        
        // Get stats by status
        let statusStatsFuture = UserNotification.query(on: req.db)
            .filter(\.$userID == userID)
            .filter(\.$deliveryStatus != nil)
            .all()
            .map { notifications in
                var statusStats: [String: Int] = [:]
                for notification in notifications {
                    if let status = notification.deliveryStatus {
                        statusStats[status, default: 0] += 1
                    }
                }
                return statusStats
            }
        
        return totalFuture.and(unreadFuture)
            .and(recentFuture)
            .and(kindStatsFuture)
            .and(channelStatsFuture)
            .and(statusStatsFuture)
            .map { ((total, unread), recent, kindStats, channelStats, statusStats) in
                return NotificationStatsResponse(
                    totalNotifications: total,
                    unreadNotifications: unread,
                    readNotifications: total - unread,
                    notificationsByKind: kindStats,
                    notificationsByChannel: channelStats,
                    notificationsByStatus: statusStats,
                    recentActivity: recent.map { NotificationHistoryResponse(from: $0) }
                )
            }
    }
    
    // MARK: - Get Single Notification
    func getNotification(req: Request) throws -> EventLoopFuture<NotificationHistoryResponse> {
        let userID = try getUserID(from: req)
        let notificationID = try req.parameters.require("notificationID", as: UUID.self)
        
        return UserNotification.query(on: req.db)
            .filter(\.$id == notificationID)
            .filter(\.$userID == userID)
            .first()
            .unwrap(or: Abort(.notFound, reason: "Notification not found"))
            .map { notification in
                return NotificationHistoryResponse(from: notification)
            }
    }
    
    // MARK: - Mark as Read
    func markAsRead(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let userID = try getUserID(from: req)
        let notificationID = try req.parameters.require("notificationID", as: UUID.self)
        
        return UserNotification.query(on: req.db)
            .filter(\.$id == notificationID)
            .filter(\.$userID == userID)
            .first()
            .unwrap(or: Abort(.notFound, reason: "Notification not found"))
            .flatMap { notification in
                notification.read = true
                return notification.update(on: req.db)
                    .transform(to: .noContent)
            }
    }
    
    // MARK: - Mark as Unread
    func markAsUnread(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let userID = try getUserID(from: req)
        let notificationID = try req.parameters.require("notificationID", as: UUID.self)
        
        return UserNotification.query(on: req.db)
            .filter(\.$id == notificationID)
            .filter(\.$userID == userID)
            .first()
            .unwrap(or: Abort(.notFound, reason: "Notification not found"))
            .flatMap { notification in
                notification.read = false
                return notification.update(on: req.db)
                    .transform(to: .noContent)
            }
    }
    
    // MARK: - Bulk Mark as Read
    func bulkMarkAsRead(req: Request) throws -> EventLoopFuture<[String: Int]> {
        let userID = try getUserID(from: req)
        let input = try req.content.decode(MarkReadRequest.self)
        
        var query = UserNotification.query(on: req.db)
            .filter(\.$userID == userID)
            .filter(\.$read == false)
        
        if let notificationIds = input.notificationIds, !notificationIds.isEmpty {
            query = query.filter(\.$id ~~ notificationIds)
        }
        
        return query.all()
            .flatMap { notifications in
                let updateFutures = notifications.map { notification in
                    notification.read = true
                    return notification.update(on: req.db)
                }
                
                return EventLoopFuture.andAllSucceed(updateFutures, on: req.eventLoop)
                    .map { _ in
                        return ["marked_as_read": notifications.count]
                    }
            }
    }
    
    // MARK: - Delete Notification
    func deleteNotification(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let userID = try getUserID(from: req)
        let notificationID = try req.parameters.require("notificationID", as: UUID.self)
        
        return UserNotification.query(on: req.db)
            .filter(\.$id == notificationID)
            .filter(\.$userID == userID)
            .first()
            .unwrap(or: Abort(.notFound, reason: "Notification not found"))
            .flatMap { notification in
                return notification.delete(on: req.db)
                    .transform(to: .noContent)
            }
    }
    
    // MARK: - Bulk Delete Notifications
    func bulkDeleteNotifications(req: Request) throws -> EventLoopFuture<[String: Int]> {
        let userID = try getUserID(from: req)
        let input = try req.content.decode(BulkDeleteRequest.self)
        
        var query = UserNotification.query(on: req.db)
            .filter(\.$userID == userID)
        
        if let notificationIds = input.notificationIds, !notificationIds.isEmpty {
            query = query.filter(\.$id ~~ notificationIds)
        } else if let olderThan = input.olderThan {
            query = query.filter(\.$createdAt < olderThan)
        } else if input.deleteAll == true {
            // Allow deleting all notifications
        } else {
            throw Abort(.badRequest, reason: "Must specify notificationIds, olderThan date, or deleteAll=true")
        }
        
        return query.all()
            .flatMap { notifications in
                let deleteFutures = notifications.map { notification in
                    notification.delete(on: req.db)
                }
                
                return EventLoopFuture.andAllSucceed(deleteFutures, on: req.eventLoop)
                    .map { _ in
                        return ["deleted": notifications.count]
                    }
            }
    }
    
    // MARK: - Export Notifications
    func exportNotifications(req: Request) throws -> EventLoopFuture<Response> {
        let userID = try getUserID(from: req)
        let format: String = req.query["format"] ?? "json"
        
        return UserNotification.query(on: req.db)
            .filter(\.$userID == userID)
            .sort(\.$createdAt, .descending)
            .all()
            .flatMapThrowing { notifications in
                let historyData = notifications.map { NotificationHistoryResponse(from: $0) }
                
                switch format.lowercased() {
                case "csv":
                    let csvData = try self.exportToCSV(historyData)
                    let response = Response(status: .ok, body: .init(string: csvData))
                    response.headers.contentType = .init(type: "text", subType: "csv")
                    response.headers.add(name: .contentDisposition, value: "attachment; filename=\"notifications.csv\"")
                    return response
                    
                default: // json
                    let jsonData = try JSONEncoder().encode(historyData)
                    let response = Response(status: .ok, body: .init(data: jsonData))
                    response.headers.contentType = .json
                    response.headers.add(name: .contentDisposition, value: "attachment; filename=\"notifications.json\"")
                    return response
                }
            }
    }
    
    // MARK: - Search Notifications
    func searchNotifications(req: Request) throws -> EventLoopFuture<Page<NotificationHistoryResponse>> {
        let userID = try getUserID(from: req)
        let searchTerm: String = try req.query.get("q")
        
        return UserNotification.query(on: req.db)
            .filter(\.$userID == userID)
            .group(.or) { group in
                group.filter(\.$title ~~ searchTerm)
                group.filter(\.$message ~~ searchTerm)
            }
            .sort(\.$createdAt, .descending)
            .paginate(for: req)
            .map { page in
                return page.map { notification in
                    return NotificationHistoryResponse(from: notification)
                }
            }
    }
    
    // MARK: - Helper Methods
    private func getUserID(from req: Request) throws -> UUID {
        // Extract user ID from JWT token or session
        // This is a placeholder - implement based on your auth system
        guard let userIDString = req.headers.first(name: "X-User-ID"),
              let userID = UUID(uuidString: userIDString) else {
            throw Abort(.unauthorized, reason: "User ID not found in request")
        }
        return userID
    }
    
    private func exportToCSV(_ notifications: [NotificationHistoryResponse]) throws -> String {
        var csv = "ID,Title,Kind,Message,Read,Entity Type,Entity ID,Delivery Channel,Delivery Status,Scheduled For,Created At\n"
        
        for notification in notifications {
            let row = [
                notification.id?.uuidString ?? "",
                notification.title.replacingOccurrences(of: "\"", with: "\"\""),
                notification.kind,
                notification.message.replacingOccurrences(of: "\"", with: "\"\""),
                notification.read ? "true" : "false",
                notification.entityType ?? "",
                notification.entityId?.uuidString ?? "",
                notification.deliveryChannel ?? "",
                notification.deliveryStatus ?? "",
                notification.scheduledFor?.ISO8601Format() ?? "",
                notification.createdAt?.ISO8601Format() ?? ""
            ].map { "\"\($0)\"" }.joined(separator: ",")
            
            csv += row + "\n"
        }
        
        return csv
    }
}
