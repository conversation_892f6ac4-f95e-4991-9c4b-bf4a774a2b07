//
//  NotificationPreferencesController.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent

// MARK: - Request/Response DTOs
struct NotificationPreferencesRequest: Content {
    let smsEnabled: Bool
    let smsPhoneNumber: String?
    let emailEnabled: Bool
    let emailAddress: String?
    let pushEnabled: Bool
    let browserEnabled: Bool
    let inAppEnabled: Bool
    let reminderHours: [Int]
    let quietHoursStart: Int?
    let quietHoursEnd: Int?
    let timezone: String?
}

struct NotificationPreferencesResponse: Content {
    let id: UUID?
    let userID: UUID
    let smsEnabled: Bool
    let smsPhoneNumber: String?
    let emailEnabled: Bool
    let emailAddress: String?
    let pushEnabled: Bool
    let browserEnabled: Bool
    let inAppEnabled: Bool
    let reminderHours: [Int]
    let quietHoursStart: Int?
    let quietHoursEnd: Int?
    let timezone: String?
    let createdAt: Date?
    let updatedAt: Date?
    
    init(from preferences: UserNotificationPreferences) throws {
        self.id = preferences.id
        self.userID = try preferences.$user.id
        self.smsEnabled = preferences.smsEnabled
        self.smsPhoneNumber = preferences.smsPhoneNumber
        self.emailEnabled = preferences.emailEnabled
        self.emailAddress = preferences.emailAddress
        self.pushEnabled = preferences.pushEnabled
        self.browserEnabled = preferences.browserEnabled
        self.inAppEnabled = preferences.inAppEnabled
        self.reminderHours = preferences.reminderHours
        self.quietHoursStart = preferences.quietHoursStart
        self.quietHoursEnd = preferences.quietHoursEnd
        self.timezone = preferences.timezone
        self.createdAt = preferences.createdAt
        self.updatedAt = preferences.updatedAt
    }
}

struct NotificationTestRequest: Content {
    let channels: [String] // ["sms", "email", "push", "browser", "in_app"]
    let message: String?
}

// MARK: - Notification Preferences Controller
struct NotificationPreferencesController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let preferences = routes.grouped("notification-preferences")
        
        // CRUD operations
        preferences.get(use: getPreferences)
        preferences.post(use: createPreferences)
        preferences.put(use: updatePreferences)
        preferences.delete(use: deletePreferences)
        
        // Utility endpoints
        preferences.post("test", use: testNotifications)
        preferences.get("channels", use: getAvailableChannels)
        preferences.get("timezones", use: getAvailableTimezones)
    }
    
    // MARK: - Get User Preferences
    func getPreferences(req: Request) throws -> EventLoopFuture<NotificationPreferencesResponse> {
        let userID = try getUserID(from: req)
        
        return UserNotificationPreferences.query(on: req.db)
            .filter(\.$user.$id == userID)
            .first()
            .unwrap(or: Abort(.notFound, reason: "Notification preferences not found"))
            .flatMapThrowing { preferences in
                return try NotificationPreferencesResponse(from: preferences)
            }
    }
    
    // MARK: - Create User Preferences
    func createPreferences(req: Request) throws -> EventLoopFuture<NotificationPreferencesResponse> {
        let userID = try getUserID(from: req)
        let input = try req.content.decode(NotificationPreferencesRequest.self)
        
        // Check if preferences already exist
        return UserNotificationPreferences.query(on: req.db)
            .filter(\.$user.$id == userID)
            .first()
            .flatMap { existingPreferences in
                if existingPreferences != nil {
                    return req.eventLoop.makeFailedFuture(
                        Abort(.conflict, reason: "Notification preferences already exist for this user")
                    )
                }
                
                let preferences = UserNotificationPreferences(
                    userID: userID,
                    smsEnabled: input.smsEnabled,
                    smsPhoneNumber: input.smsPhoneNumber,
                    emailEnabled: input.emailEnabled,
                    emailAddress: input.emailAddress,
                    pushEnabled: input.pushEnabled,
                    browserEnabled: input.browserEnabled,
                    inAppEnabled: input.inAppEnabled,
                    reminderHours: input.reminderHours,
                    quietHoursStart: input.quietHoursStart,
                    quietHoursEnd: input.quietHoursEnd,
                    timezone: input.timezone
                )
                
                return preferences.save(on: req.db)
                    .flatMapThrowing { _ in
                        return try NotificationPreferencesResponse(from: preferences)
                    }
            }
    }
    
    // MARK: - Update User Preferences
    func updatePreferences(req: Request) throws -> EventLoopFuture<NotificationPreferencesResponse> {
        let userID = try getUserID(from: req)
        let input = try req.content.decode(NotificationPreferencesRequest.self)
        
        return UserNotificationPreferences.query(on: req.db)
            .filter(\.$user.$id == userID)
            .first()
            .unwrap(or: Abort(.notFound, reason: "Notification preferences not found"))
            .flatMap { preferences in
                // Update all fields
                preferences.smsEnabled = input.smsEnabled
                preferences.smsPhoneNumber = input.smsPhoneNumber
                preferences.emailEnabled = input.emailEnabled
                preferences.emailAddress = input.emailAddress
                preferences.pushEnabled = input.pushEnabled
                preferences.browserEnabled = input.browserEnabled
                preferences.inAppEnabled = input.inAppEnabled
                preferences.reminderHours = input.reminderHours
                preferences.quietHoursStart = input.quietHoursStart
                preferences.quietHoursEnd = input.quietHoursEnd
                preferences.timezone = input.timezone
                
                return preferences.update(on: req.db)
                    .flatMapThrowing { _ in
                        return try NotificationPreferencesResponse(from: preferences)
                    }
            }
    }
    
    // MARK: - Delete User Preferences
    func deletePreferences(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let userID = try getUserID(from: req)
        
        return UserNotificationPreferences.query(on: req.db)
            .filter(\.$user.$id == userID)
            .first()
            .unwrap(or: Abort(.notFound, reason: "Notification preferences not found"))
            .flatMap { preferences in
                return preferences.delete(on: req.db)
                    .transform(to: .noContent)
            }
    }
    
    // MARK: - Test Notifications
    func testNotifications(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let userID = try getUserID(from: req)
        let input = try req.content.decode(NotificationTestRequest.self)
        
        let channels = input.channels.compactMap { DeliveryChannel(rawValue: $0) }
        let message = input.message ?? "This is a test notification from Wellup!"
        
        let notification = NotificationPayload(
            title: "Test Notification",
            message: message,
            entityType: .task,
            entityID: UUID(),
            userID: userID,
            dueDate: Date().addingTimeInterval(86400),
            reminderHoursBefore: 24,
            metadata: ["test": "true"]
        )
        
        return req.notificationService.sendNotification(notification, via: channels, on: req)
            .transform(to: .accepted)
    }
    
    // MARK: - Get Available Channels
    func getAvailableChannels(req: Request) throws -> [String: Any] {
        return [
            "channels": DeliveryChannel.allCases.map { $0.rawValue },
            "descriptions": [
                "sms": "Text message notifications",
                "email": "Email notifications",
                "push": "Mobile push notifications",
                "browser": "Browser push notifications",
                "in_app": "In-app notifications"
            ]
        ]
    }
    
    // MARK: - Get Available Timezones
    func getAvailableTimezones(req: Request) throws -> [String] {
        return [
            "America/New_York",
            "America/Chicago",
            "America/Denver",
            "America/Los_Angeles",
            "America/Phoenix",
            "America/Anchorage",
            "Pacific/Honolulu",
            "UTC"
        ]
    }
    
    // MARK: - Helper Methods
    private func getUserID(from req: Request) throws -> UUID {
        // Extract user ID from JWT token or session
        // This is a placeholder - implement based on your auth system
        guard let userIDString = req.headers.first(name: "X-User-ID"),
              let userID = UUID(uuidString: userIDString) else {
            throw Abort(.unauthorized, reason: "User ID not found in request")
        }
        return userID
    }
}
