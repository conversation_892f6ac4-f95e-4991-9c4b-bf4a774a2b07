//
//  HealthCheckController.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent
import SotoSQS
import SotoEventBridge

// MARK: - Health Check Response
struct HealthCheckResponse: Content {
    let status: String
    let timestamp: Date
    let version: String
    let environment: String
    let components: [ComponentHealth]
    let overall: OverallHealth
}

struct ComponentHealth: Content {
    let name: String
    let status: String
    let responseTime: Double?
    let error: String?
    let lastChecked: Date
}

struct OverallHealth: Content {
    let status: String
    let healthyComponents: Int
    let totalComponents: Int
    let uptime: Double?
}

// MARK: - Health Check Controller
struct HealthCheckController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let health = routes.grouped("health")
        
        health.get(use: healthCheck)
        health.get("detailed", use: detailedHealthCheck)
        health.get("components", ":component", use: componentHealthCheck)
    }
    
    // MARK: - Basic Health Check
    func healthCheck(req: Request) throws -> EventLoop<PERSON><HealthCheckResponse> {
        let startTime = Date()
        
        // Quick health checks
        let dbHealthFuture = checkDatabaseHealth(on: req)
        let sqsHealthFuture = checkSQSHealth(on: req)
        
        return dbHealthFuture.and(sqsHealthFuture)
            .map { (dbHealth, sqsHealth) in
                let components = [dbHealth, sqsHealth]
                let healthyCount = components.filter { $0.status == "healthy" }.count
                
                let overall = OverallHealth(
                    status: healthyCount == components.count ? "healthy" : "degraded",
                    healthyComponents: healthyCount,
                    totalComponents: components.count,
                    uptime: Date().timeIntervalSince(startTime)
                )
                
                return HealthCheckResponse(
                    status: overall.status,
                    timestamp: Date(),
                    version: Environment.get("APP_VERSION") ?? "unknown",
                    environment: Environment.get("ENVIRONMENT") ?? "unknown",
                    components: components,
                    overall: overall
                )
            }
    }
    
    // MARK: - Detailed Health Check
    func detailedHealthCheck(req: Request) throws -> EventLoopFuture<HealthCheckResponse> {
        let startTime = Date()
        
        // Comprehensive health checks
        let dbHealthFuture = checkDatabaseHealth(on: req)
        let sqsHealthFuture = checkSQSHealth(on: req)
        let eventBridgeHealthFuture = checkEventBridgeHealth(on: req)
        let twilioHealthFuture = checkTwilioHealth(on: req)
        let sendGridHealthFuture = checkSendGridHealth(on: req)
        
        return dbHealthFuture
            .and(sqsHealthFuture)
            .and(eventBridgeHealthFuture)
            .and(twilioHealthFuture)
            .and(sendGridHealthFuture)
            .map { ((dbHealth, sqsHealth), eventBridgeHealth, twilioHealth, sendGridHealth) in
                let components = [dbHealth, sqsHealth, eventBridgeHealth, twilioHealth, sendGridHealth]
                let healthyCount = components.filter { $0.status == "healthy" }.count
                
                // Record health check metrics
                for component in components {
                    _ = req.notificationMonitoring.recordHealthCheck(
                        component: component.name,
                        healthy: component.status == "healthy",
                        responseTimeMs: component.responseTime
                    )
                }
                
                let overall = OverallHealth(
                    status: healthyCount == components.count ? "healthy" : 
                           healthyCount > 0 ? "degraded" : "unhealthy",
                    healthyComponents: healthyCount,
                    totalComponents: components.count,
                    uptime: Date().timeIntervalSince(startTime)
                )
                
                return HealthCheckResponse(
                    status: overall.status,
                    timestamp: Date(),
                    version: Environment.get("APP_VERSION") ?? "unknown",
                    environment: Environment.get("ENVIRONMENT") ?? "unknown",
                    components: components,
                    overall: overall
                )
            }
    }
    
    // MARK: - Component Health Check
    func componentHealthCheck(req: Request) throws -> EventLoopFuture<ComponentHealth> {
        let component = try req.parameters.require("component")
        
        switch component.lowercased() {
        case "database", "db":
            return checkDatabaseHealth(on: req)
        case "sqs", "queue":
            return checkSQSHealth(on: req)
        case "eventbridge", "scheduler":
            return checkEventBridgeHealth(on: req)
        case "twilio", "sms":
            return checkTwilioHealth(on: req)
        case "sendgrid", "email":
            return checkSendGridHealth(on: req)
        default:
            throw Abort(.badRequest, reason: "Unknown component: \(component)")
        }
    }
    
    // MARK: - Individual Health Checks
    private func checkDatabaseHealth(on req: Request) -> EventLoopFuture<ComponentHealth> {
        let startTime = Date()
        
        return req.db.raw("SELECT 1").all()
            .map { _ in
                let responseTime = Date().timeIntervalSince(startTime) * 1000
                return ComponentHealth(
                    name: "database",
                    status: "healthy",
                    responseTime: responseTime,
                    error: nil,
                    lastChecked: Date()
                )
            }
            .recover { error in
                let responseTime = Date().timeIntervalSince(startTime) * 1000
                return ComponentHealth(
                    name: "database",
                    status: "unhealthy",
                    responseTime: responseTime,
                    error: error.localizedDescription,
                    lastChecked: Date()
                )
            }
    }
    
    private func checkSQSHealth(on req: Request) -> EventLoopFuture<ComponentHealth> {
        let startTime = Date()
        let sqsClient = SQS(client: req.application.aws.client, region: .useast1)
        let queueUrl = Environment.get("NOTIFICATION_RETRY_QUEUE_URL") ?? ""
        
        guard !queueUrl.isEmpty else {
            return req.eventLoop.makeSucceededFuture(
                ComponentHealth(
                    name: "sqs",
                    status: "unhealthy",
                    responseTime: nil,
                    error: "Queue URL not configured",
                    lastChecked: Date()
                )
            )
        }
        
        let getAttributesInput = SQS.GetQueueAttributesRequest(
            attributeNames: [.approximateNumberOfMessages],
            queueUrl: queueUrl
        )
        
        return sqsClient.getQueueAttributes(getAttributesInput)
            .map { _ in
                let responseTime = Date().timeIntervalSince(startTime) * 1000
                return ComponentHealth(
                    name: "sqs",
                    status: "healthy",
                    responseTime: responseTime,
                    error: nil,
                    lastChecked: Date()
                )
            }
            .recover { error in
                let responseTime = Date().timeIntervalSince(startTime) * 1000
                return ComponentHealth(
                    name: "sqs",
                    status: "unhealthy",
                    responseTime: responseTime,
                    error: error.localizedDescription,
                    lastChecked: Date()
                )
            }
    }
    
    private func checkEventBridgeHealth(on req: Request) -> EventLoopFuture<ComponentHealth> {
        let startTime = Date()
        let eventBridgeClient = EventBridge(client: req.application.aws.client, region: .useast1)
        
        // List schedules to test EventBridge connectivity
        let listSchedulesInput = EventBridge.ListSchedulesInput(maxResults: 1)
        
        return eventBridgeClient.listSchedules(listSchedulesInput)
            .map { _ in
                let responseTime = Date().timeIntervalSince(startTime) * 1000
                return ComponentHealth(
                    name: "eventbridge",
                    status: "healthy",
                    responseTime: responseTime,
                    error: nil,
                    lastChecked: Date()
                )
            }
            .recover { error in
                let responseTime = Date().timeIntervalSince(startTime) * 1000
                return ComponentHealth(
                    name: "eventbridge",
                    status: "unhealthy",
                    responseTime: responseTime,
                    error: error.localizedDescription,
                    lastChecked: Date()
                )
            }
    }
    
    private func checkTwilioHealth(on req: Request) -> EventLoopFuture<ComponentHealth> {
        let startTime = Date()
        
        // Simple check - verify Twilio credentials are configured
        guard let accountSid = Environment.get("TWILIO_ACCOUNT_SID"),
              let authToken = Environment.get("TWILIO_AUTH_TOKEN"),
              !accountSid.isEmpty, !authToken.isEmpty else {
            return req.eventLoop.makeSucceededFuture(
                ComponentHealth(
                    name: "twilio",
                    status: "unhealthy",
                    responseTime: nil,
                    error: "Twilio credentials not configured",
                    lastChecked: Date()
                )
            )
        }
        
        // For a more thorough check, you could make an API call to Twilio
        // For now, we'll just verify configuration
        let responseTime = Date().timeIntervalSince(startTime) * 1000
        return req.eventLoop.makeSucceededFuture(
            ComponentHealth(
                name: "twilio",
                status: "healthy",
                responseTime: responseTime,
                error: nil,
                lastChecked: Date()
            )
        )
    }
    
    private func checkSendGridHealth(on req: Request) -> EventLoopFuture<ComponentHealth> {
        let startTime = Date()
        
        // Simple check - verify SendGrid API key is configured
        guard let apiKey = Environment.get("SENDGRID_API_KEY"),
              !apiKey.isEmpty else {
            return req.eventLoop.makeSucceededFuture(
                ComponentHealth(
                    name: "sendgrid",
                    status: "unhealthy",
                    responseTime: nil,
                    error: "SendGrid API key not configured",
                    lastChecked: Date()
                )
            )
        }
        
        // For a more thorough check, you could make an API call to SendGrid
        // For now, we'll just verify configuration
        let responseTime = Date().timeIntervalSince(startTime) * 1000
        return req.eventLoop.makeSucceededFuture(
            ComponentHealth(
                name: "sendgrid",
                status: "healthy",
                responseTime: responseTime,
                error: nil,
                lastChecked: Date()
            )
        )
    }
}
