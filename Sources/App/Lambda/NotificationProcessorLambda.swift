//
//  NotificationProcessorLambda.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent
import AWSLambdaRuntime
import AWSLambdaEvents

// MARK: - Lambda Event Structures
struct NotificationEvent: Codable {
    let scheduleId: String
    let entityType: String
    let entityId: String
    let userId: String
    let reminderHoursBefore: Int
    let timestamp: String?
}

struct NotificationLambdaResponse: Codable {
    let statusCode: Int
    let body: String
    let headers: [String: String]?
}

// MARK: - <PERSON><PERSON>ler
@main
struct NotificationProcessorLambda {
    static func main() async throws {
        let runtime = LambdaRuntime.init { context, event in
            return await handleNotificationEvent(context: context, event: event)
        }
        
        try await runtime.run()
    }
    
    static func handleNotificationEvent(
        context: LambdaContext,
        event: NotificationEvent
    ) async -> Result<NotificationLambdaResponse, Error> {
        context.logger.info("Processing notification event for schedule: \(event.scheduleId)")
        
        do {
            // Initialize Vapor application for database access
            let app = try await createVaporApp()
            defer { app.shutdown() }
            
            // Process the notification
            try await processNotification(event: event, app: app, logger: context.logger)
            
            return .success(NotificationLambdaResponse(
                statusCode: 200,
                body: "Notification processed successfully",
                headers: ["Content-Type": "application/json"]
            ))
            
        } catch {
            context.logger.error("Failed to process notification: \(error)")
            
            // Send to SQS retry queue
            try await sendToRetryQueue(event: event, error: error, logger: context.logger)
            
            return .failure(error)
        }
    }
    
    private static func createVaporApp() async throws -> Application {
        let app = Application(.production)
        
        // Configure database
        app.databases.use(.postgres(
            hostname: Environment.get("DATABASE_HOST") ?? "localhost",
            port: Environment.get("DATABASE_PORT").flatMap(Int.init(_:)) ?? 5432,
            username: Environment.get("DATABASE_USERNAME") ?? "vapor_username",
            password: Environment.get("DATABASE_PASSWORD") ?? "vapor_password",
            database: Environment.get("DATABASE_NAME") ?? "vapor_database"
        ), as: .psql)
        
        // Add migrations
        app.migrations.add(UserNotificationPreferencesMigration())
        app.migrations.add(NotificationScheduleMigration())
        app.migrations.add(EnhancedUserNotificationMigration())
        
        // Configure AWS client
        app.aws.client = app.aws.clientOrInit
        
        return app
    }
    
    private static func processNotification(
        event: NotificationEvent,
        app: Application,
        logger: Logger
    ) async throws {
        guard let scheduleUUID = UUID(uuidString: event.scheduleId) else {
            throw NotificationError.invalidScheduleID
        }
        
        // Find the notification schedule
        guard let schedule = try await NotificationSchedule.find(scheduleUUID, on: app.db) else {
            throw NotificationError.scheduleNotFound
        }
        
        // Verify the schedule is still active
        guard schedule.status == ScheduleStatus.scheduled.rawValue else {
            logger.info("Schedule \(event.scheduleId) is no longer active (status: \(schedule.status))")
            return
        }
        
        // Get user notification preferences
        guard let preferences = try await UserNotificationPreferences.query(on: app.db)
            .filter(\.$user.$id == schedule.$user.id)
            .first() else {
            throw NotificationError.preferencesNotFound
        }
        
        // Check if we're in quiet hours
        if isInQuietHours(preferences: preferences) {
            logger.info("Delaying notification due to quiet hours")
            try await rescheduleForAfterQuietHours(schedule: schedule, preferences: preferences, app: app)
            return
        }
        
        // Create notification payload
        let payload = NotificationPayload(
            title: generateNotificationTitle(for: schedule),
            message: generateNotificationMessage(for: schedule),
            entityType: EntityType(rawValue: schedule.entityType) ?? .task,
            entityID: schedule.entityID,
            userID: try schedule.$user.id,
            dueDate: schedule.dueDate,
            reminderHoursBefore: schedule.reminderHoursBefore,
            metadata: [
                "schedule_id": event.scheduleId,
                "entity_title": schedule.entityTitle
            ]
        )
        
        // Determine delivery channels
        let channels = getEnabledChannels(from: preferences)
        
        // Create a mock request for the notification service
        let mockRequest = try createMockRequest(app: app)
        
        // Deliver notifications
        let deliveryService = NotificationDeliveryService(awsClient: app.aws.client, logger: logger)
        let results = try await deliveryService.deliverNotification(payload, via: channels, on: mockRequest).get()
        
        // Update schedule status based on delivery results
        let hasSuccessfulDelivery = results.contains { $0.success }
        
        if hasSuccessfulDelivery {
            schedule.status = ScheduleStatus.sent.rawValue
            logger.info("Notification delivered successfully for schedule \(event.scheduleId)")
        } else {
            schedule.status = ScheduleStatus.failed.rawValue
            schedule.lastError = "All delivery channels failed"
            schedule.deliveryAttempts = (schedule.deliveryAttempts ?? 0) + 1
            logger.error("All delivery channels failed for schedule \(event.scheduleId)")
        }
        
        try await schedule.update(on: app.db)
        
        // Log delivery results
        for result in results {
            if result.success {
                logger.info("Successfully delivered via \(result.channel.rawValue)")
            } else {
                logger.error("Failed to deliver via \(result.channel.rawValue): \(result.error ?? "Unknown error")")
            }
        }
    }
    
    private static func isInQuietHours(preferences: UserNotificationPreferences) -> Bool {
        guard let startHour = preferences.quietHoursStart,
              let endHour = preferences.quietHoursEnd else {
            return false
        }
        
        let calendar = Calendar.current
        let now = Date()
        let currentHour = calendar.component(.hour, from: now)
        
        if startHour <= endHour {
            // Same day quiet hours (e.g., 22:00 to 08:00 next day)
            return currentHour >= startHour || currentHour < endHour
        } else {
            // Overnight quiet hours (e.g., 10:00 PM to 8:00 AM)
            return currentHour >= startHour && currentHour < endHour
        }
    }
    
    private static func rescheduleForAfterQuietHours(
        schedule: NotificationSchedule,
        preferences: UserNotificationPreferences,
        app: Application
    ) async throws {
        guard let endHour = preferences.quietHoursEnd else { return }
        
        let calendar = Calendar.current
        let now = Date()
        
        // Schedule for the end of quiet hours
        var components = calendar.dateComponents([.year, .month, .day], from: now)
        components.hour = endHour
        components.minute = 0
        components.second = 0
        
        guard let newScheduledTime = calendar.date(from: components) else { return }
        
        // If the time has already passed today, schedule for tomorrow
        let finalScheduledTime = newScheduledTime <= now ? 
            calendar.date(byAdding: .day, value: 1, to: newScheduledTime) ?? newScheduledTime : 
            newScheduledTime
        
        schedule.scheduledTime = finalScheduledTime
        try await schedule.update(on: app.db)
        
        // Create new EventBridge schedule
        // This would require additional EventBridge integration
    }
    
    private static func generateNotificationTitle(for schedule: NotificationSchedule) -> String {
        let entityType = schedule.entityType.capitalized
        let timeDescription = schedule.reminderHoursBefore == 24 ? "tomorrow" : "in \(schedule.reminderHoursBefore) hours"
        
        return "\(entityType) Due \(timeDescription.capitalized)"
    }
    
    private static func generateNotificationMessage(for schedule: NotificationSchedule) -> String {
        let timeDescription = schedule.reminderHoursBefore == 24 ? "tomorrow" : "in \(schedule.reminderHoursBefore) hours"
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        
        return "Your \(schedule.entityType) '\(schedule.entityTitle)' is due \(timeDescription) (\(formatter.string(from: schedule.dueDate)))."
    }
    
    private static func getEnabledChannels(from preferences: UserNotificationPreferences) -> [DeliveryChannel] {
        var channels: [DeliveryChannel] = []
        
        if preferences.smsEnabled && preferences.smsPhoneNumber != nil {
            channels.append(.sms)
        }
        
        if preferences.emailEnabled && preferences.emailAddress != nil {
            channels.append(.email)
        }
        
        if preferences.pushEnabled {
            channels.append(.push)
        }
        
        if preferences.browserEnabled {
            channels.append(.browser)
        }
        
        if preferences.inAppEnabled {
            channels.append(.inApp)
        }
        
        return channels
    }
    
    private static func createMockRequest(app: Application) throws -> Request {
        let headers = HTTPHeaders()
        let body = ByteBuffer()
        
        return Request(
            application: app,
            method: .POST,
            url: URI(string: "/lambda/notification"),
            headers: headers,
            collectedBody: body,
            on: app.eventLoopGroup.next()
        )
    }
    
    private static func sendToRetryQueue(
        event: NotificationEvent,
        error: Error,
        logger: Logger
    ) async throws {
        // Implementation for sending failed notifications to SQS retry queue
        // This would be implemented in the SQS retry system task
        logger.error("Sending notification \(event.scheduleId) to retry queue due to error: \(error)")
    }
}

// MARK: - Error Types
enum NotificationError: Error, LocalizedError {
    case invalidScheduleID
    case scheduleNotFound
    case preferencesNotFound
    case deliveryFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidScheduleID:
            return "Invalid schedule ID format"
        case .scheduleNotFound:
            return "Notification schedule not found"
        case .preferencesNotFound:
            return "User notification preferences not found"
        case .deliveryFailed:
            return "Notification delivery failed"
        }
    }
}
