# AWS Lambda Deployment Configuration for Notification Service
# This file contains the infrastructure as code for deploying the notification Lambda functions

AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: 'Wellup Notification Service Lambda Functions'

Parameters:
  Environment:
    Type: String
    Default: staging
    AllowedValues: [staging, production]
    Description: Environment name
  
  DatabaseHost:
    Type: String
    Description: PostgreSQL database host
  
  DatabaseName:
    Type: String
    Description: PostgreSQL database name
  
  DatabaseUsername:
    Type: String
    Description: PostgreSQL database username
  
  DatabasePassword:
    Type: String
    NoEcho: true
    Description: PostgreSQL database password
  
  SendGridApiKey:
    Type: String
    NoEcho: true
    Description: SendGrid API key for email notifications
  
  TwilioAccountSid:
    Type: String
    NoEcho: true
    Description: Twilio Account SID for SMS notifications
  
  TwilioAuthToken:
    Type: String
    NoEcho: true
    Description: Twilio Auth Token for SMS notifications

Globals:
  Function:
    Timeout: 300
    MemorySize: 512
    Runtime: provided.al2
    Architectures:
      - arm64
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        DATABASE_HOST: !Ref DatabaseHost
        DATABASE_NAME: !Ref DatabaseName
        DATABASE_USERNAME: !Ref DatabaseUsername
        DATABASE_PASSWORD: !Ref DatabasePassword
        SENDGRID_API_KEY: !Ref SendGridApiKey
        TWILIO_ACCOUNT_SID: !Ref TwilioAccountSid
        TWILIO_AUTH_TOKEN: !Ref TwilioAuthToken

Resources:
  # IAM Role for Lambda Functions
  NotificationLambdaRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        - arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole
      Policies:
        - PolicyName: NotificationServicePolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - sqs:SendMessage
                  - sqs:ReceiveMessage
                  - sqs:DeleteMessage
                  - sqs:GetQueueAttributes
                Resource: 
                  - !GetAtt NotificationRetryQueue.Arn
                  - !GetAtt NotificationDLQ.Arn
              - Effect: Allow
                Action:
                  - sns:Publish
                  - sns:CreatePlatformEndpoint
                  - sns:Subscribe
                Resource: '*'
              - Effect: Allow
                Action:
                  - pinpoint:SendMessages
                  - pinpoint:GetApp
                Resource: '*'
              - Effect: Allow
                Action:
                  - scheduler:CreateSchedule
                  - scheduler:DeleteSchedule
                  - scheduler:GetSchedule
                  - scheduler:UpdateSchedule
                Resource: '*'
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: '*'

  # EventBridge Role for invoking Lambda
  EventBridgeRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: scheduler.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: InvokeLambdaPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - lambda:InvokeFunction
                Resource: !GetAtt NotificationProcessorFunction.Arn

  # SQS Queues
  NotificationRetryQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub 'notification-retry-queue-${Environment}'
      VisibilityTimeoutSeconds: 360
      MessageRetentionPeriod: 1209600  # 14 days
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt NotificationDLQ.Arn
        maxReceiveCount: 3

  NotificationDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub 'notification-dlq-${Environment}'
      MessageRetentionPeriod: 1209600  # 14 days

  # Lambda Functions
  NotificationProcessorFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'notification-processor-${Environment}'
      CodeUri: ./notification-processor/
      Handler: bootstrap
      Role: !GetAtt NotificationLambdaRole.Arn
      Environment:
        Variables:
          NOTIFICATION_LAMBDA_ARN: !Sub '${AWS::StackName}-notification-processor-${Environment}'
          EVENTBRIDGE_ROLE_ARN: !GetAtt EventBridgeRole.Arn
          NOTIFICATION_RETRY_QUEUE_URL: !Ref NotificationRetryQueue
      Events:
        # This function is invoked by EventBridge Scheduler
        # No direct event source mapping needed
      DeadLetterQueue:
        Type: SQS
        TargetArn: !GetAtt NotificationDLQ.Arn

  NotificationRetryFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'notification-retry-${Environment}'
      CodeUri: ./notification-retry/
      Handler: bootstrap
      Role: !GetAtt NotificationLambdaRole.Arn
      Environment:
        Variables:
          NOTIFICATION_RETRY_QUEUE_URL: !Ref NotificationRetryQueue
      Events:
        SQSEvent:
          Type: SQS
          Properties:
            Queue: !GetAtt NotificationRetryQueue.Arn
            BatchSize: 10
            MaximumBatchingWindowInSeconds: 5
      DeadLetterQueue:
        Type: SQS
        TargetArn: !GetAtt NotificationDLQ.Arn

  # CloudWatch Log Groups
  NotificationProcessorLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/notification-processor-${Environment}'
      RetentionInDays: 30

  NotificationRetryLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/notification-retry-${Environment}'
      RetentionInDays: 30

  # CloudWatch Alarms
  NotificationProcessorErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-processor-errors-${Environment}'
      AlarmDescription: 'Notification processor function errors'
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 5
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Ref NotificationProcessorFunction

  NotificationRetryErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-retry-errors-${Environment}'
      AlarmDescription: 'Notification retry function errors'
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 5
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Ref NotificationRetryFunction

  # DLQ Alarm
  DLQMessageAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub 'notification-dlq-messages-${Environment}'
      AlarmDescription: 'Messages in notification DLQ'
      MetricName: ApproximateNumberOfVisibleMessages
      Namespace: AWS/SQS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 1
      Threshold: 1
      ComparisonOperator: GreaterThanOrEqualToThreshold
      Dimensions:
        - Name: QueueName
          Value: !GetAtt NotificationDLQ.QueueName

Outputs:
  NotificationProcessorFunctionArn:
    Description: 'Notification Processor Lambda Function ARN'
    Value: !GetAtt NotificationProcessorFunction.Arn
    Export:
      Name: !Sub '${AWS::StackName}-NotificationProcessorFunctionArn'

  NotificationRetryFunctionArn:
    Description: 'Notification Retry Lambda Function ARN'
    Value: !GetAtt NotificationRetryFunction.Arn
    Export:
      Name: !Sub '${AWS::StackName}-NotificationRetryFunctionArn'

  EventBridgeRoleArn:
    Description: 'EventBridge Role ARN for Lambda invocation'
    Value: !GetAtt EventBridgeRole.Arn
    Export:
      Name: !Sub '${AWS::StackName}-EventBridgeRoleArn'

  NotificationRetryQueueUrl:
    Description: 'Notification Retry Queue URL'
    Value: !Ref NotificationRetryQueue
    Export:
      Name: !Sub '${AWS::StackName}-NotificationRetryQueueUrl'

  NotificationDLQUrl:
    Description: 'Notification Dead Letter Queue URL'
    Value: !Ref NotificationDLQ
    Export:
      Name: !Sub '${AWS::StackName}-NotificationDLQUrl'
