#!/bin/bash

# Build script for Wellup Notification Service Lambda functions
# This script builds the Swift Lambda functions for deployment to AWS

set -e

# Configuration
SWIFT_VERSION="5.9"
LAMBDA_RUNTIME="provided.al2"
ARCHITECTURE="arm64"
BUILD_DIR="./lambda-build"
DIST_DIR="./lambda-dist"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Building Wellup Notification Service Lambda Functions${NC}"

# Clean previous builds
echo -e "${YELLOW}🧹 Cleaning previous builds...${NC}"
rm -rf $BUILD_DIR
rm -rf $DIST_DIR
mkdir -p $BUILD_DIR
mkdir -p $DIST_DIR

# Function to build a Lambda function
build_lambda() {
    local function_name=$1
    local source_file=$2
    
    echo -e "${YELLOW}📦 Building $function_name...${NC}"
    
    # Create function-specific build directory
    local func_build_dir="$BUILD_DIR/$function_name"
    mkdir -p $func_build_dir
    
    # Copy source files
    cp $source_file $func_build_dir/
    cp -r Sources/App/Models $func_build_dir/
    cp -r Sources/App/Services $func_build_dir/
    cp -r Sources/App/Extensions $func_build_dir/
    cp Package.swift $func_build_dir/
    
    # Create a minimal Package.swift for the Lambda function
    cat > $func_build_dir/Package.swift << EOF
// swift-tools-version:5.9
import PackageDescription

let package = Package(
    name: "$function_name",
    platforms: [
        .macOS(.v12)
    ],
    products: [
        .executable(name: "$function_name", targets: ["$function_name"])
    ],
    dependencies: [
        .package(url: "https://github.com/swift-server/swift-aws-lambda-runtime.git", from: "1.0.0"),
        .package(url: "https://github.com/swift-server/swift-aws-lambda-events.git", from: "0.1.0"),
        .package(url: "https://github.com/vapor/vapor.git", from: "4.0.0"),
        .package(url: "https://github.com/vapor/fluent.git", from: "4.0.0"),
        .package(url: "https://github.com/vapor/fluent-postgres-driver.git", from: "2.0.0"),
        .package(url: "https://github.com/soto-project/soto.git", from: "6.0.0"),
    ],
    targets: [
        .executableTarget(
            name: "$function_name",
            dependencies: [
                .product(name: "AWSLambdaRuntime", package: "swift-aws-lambda-runtime"),
                .product(name: "AWSLambdaEvents", package: "swift-aws-lambda-events"),
                .product(name: "Vapor", package: "vapor"),
                .product(name: "Fluent", package: "fluent"),
                .product(name: "FluentPostgresDriver", package: "fluent-postgres-driver"),
                .product(name: "SotoSNS", package: "soto"),
                .product(name: "SotoSQS", package: "soto"),
                .product(name: "SotoPinpoint", package: "soto"),
                .product(name: "SotoEventBridge", package: "soto"),
            ]
        )
    ]
)
EOF

    # Build using Docker for Linux compatibility
    echo -e "${YELLOW}🐳 Building $function_name with Docker...${NC}"
    
    docker run --rm \
        -v "$PWD/$func_build_dir":/workspace \
        -w /workspace \
        swift:$SWIFT_VERSION-amazonlinux2 \
        bash -c "
            yum update -y && \
            yum install -y git && \
            swift build --product $function_name -c release --static-swift-stdlib && \
            cp .build/release/$function_name bootstrap
        "
    
    # Create deployment package
    local func_dist_dir="$DIST_DIR/$function_name"
    mkdir -p $func_dist_dir
    
    cp $func_build_dir/bootstrap $func_dist_dir/
    chmod +x $func_dist_dir/bootstrap
    
    # Create ZIP package
    cd $func_dist_dir
    zip -r ../$function_name.zip .
    cd - > /dev/null
    
    echo -e "${GREEN}✅ $function_name built successfully${NC}"
}

# Build notification processor Lambda
build_lambda "notification-processor" "Sources/App/Lambda/NotificationProcessorLambda.swift"

# Build notification retry Lambda
build_lambda "notification-retry" "Sources/App/Lambda/NotificationRetryLambda.swift"

echo -e "${GREEN}🎉 All Lambda functions built successfully!${NC}"
echo -e "${YELLOW}📁 Distribution files:${NC}"
ls -la $DIST_DIR/

# Create deployment script
cat > $DIST_DIR/deploy.sh << 'EOF'
#!/bin/bash

# Deployment script for Wellup Notification Service Lambda functions
# Usage: ./deploy.sh <environment> <aws-profile>

set -e

ENVIRONMENT=${1:-staging}
AWS_PROFILE=${2:-default}

echo "🚀 Deploying Notification Service Lambda functions to $ENVIRONMENT environment"

# Deploy using SAM
sam deploy \
    --template-file ../Sources/App/Lambda/lambda-deployment.yml \
    --stack-name "wellup-notification-service-$ENVIRONMENT" \
    --capabilities CAPABILITY_IAM \
    --parameter-overrides \
        Environment=$ENVIRONMENT \
        DatabaseHost=$DATABASE_HOST \
        DatabaseName=$DATABASE_NAME \
        DatabaseUsername=$DATABASE_USERNAME \
        DatabasePassword=$DATABASE_PASSWORD \
        SendGridApiKey=$SENDGRID_API_KEY \
        TwilioAccountSid=$TWILIO_ACCOUNT_SID \
        TwilioAuthToken=$TWILIO_AUTH_TOKEN \
    --profile $AWS_PROFILE \
    --region us-east-1

echo "✅ Deployment completed successfully!"
EOF

chmod +x $DIST_DIR/deploy.sh

echo -e "${GREEN}📋 Deployment Instructions:${NC}"
echo -e "1. Set environment variables:"
echo -e "   export DATABASE_HOST=your-db-host"
echo -e "   export DATABASE_NAME=your-db-name"
echo -e "   export DATABASE_USERNAME=your-db-username"
echo -e "   export DATABASE_PASSWORD=your-db-password"
echo -e "   export SENDGRID_API_KEY=your-sendgrid-key"
echo -e "   export TWILIO_ACCOUNT_SID=your-twilio-sid"
echo -e "   export TWILIO_AUTH_TOKEN=your-twilio-token"
echo -e ""
echo -e "2. Run deployment:"
echo -e "   cd $DIST_DIR"
echo -e "   ./deploy.sh staging your-aws-profile"
echo -e ""
echo -e "${YELLOW}⚠️  Make sure you have AWS SAM CLI installed and configured${NC}"
