//
//  NotificationRetryLambda.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Vapor
import Fluent
import AWSLambdaRuntime
import AWSLambdaEvents
import SotoSQS

// MARK: - SQS Event Structures
struct SQSNotificationEvent: Codable {
    let scheduleId: String
    let entityType: String
    let entityId: String
    let userId: String
    let reminderHoursBefore: Int
    let originalError: String
    let retryAttempt: Int
    let maxRetries: Int
    let delaySeconds: Int
}

struct RetryLambdaResponse: Codable {
    let statusCode: Int
    let body: String
    let processedRecords: Int
    let failedRecords: Int
}

// MARK: - Retry Lambda Handler
@main
struct NotificationRetryLambda {
    static func main() async throws {
        let runtime = LambdaRuntime.init { context, event in
            return await handleSQSEvent(context: context, event: event)
        }
        
        try await runtime.run()
    }
    
    static func handleSQSEvent(
        context: LambdaContext,
        event: SQSEvent
    ) async -> Result<RetryLambdaResponse, Error> {
        context.logger.info("Processing \(event.records.count) SQS records for notification retry")
        
        var processedRecords = 0
        var failedRecords = 0
        
        do {
            // Initialize Vapor application
            let app = try await createVaporApp()
            defer { app.shutdown() }
            
            // Process each SQS record
            for record in event.records {
                do {
                    try await processRetryRecord(record: record, app: app, logger: context.logger)
                    processedRecords += 1
                } catch {
                    context.logger.error("Failed to process record \(record.messageId ?? "unknown"): \(error)")
                    failedRecords += 1
                }
            }
            
            return .success(RetryLambdaResponse(
                statusCode: 200,
                body: "Processed \(processedRecords) records, \(failedRecords) failed",
                processedRecords: processedRecords,
                failedRecords: failedRecords
            ))
            
        } catch {
            context.logger.error("Failed to initialize application: \(error)")
            return .failure(error)
        }
    }
    
    private static func createVaporApp() async throws -> Application {
        let app = Application(.production)
        
        // Configure database
        app.databases.use(.postgres(
            hostname: Environment.get("DATABASE_HOST") ?? "localhost",
            port: Environment.get("DATABASE_PORT").flatMap(Int.init(_:)) ?? 5432,
            username: Environment.get("DATABASE_USERNAME") ?? "vapor_username",
            password: Environment.get("DATABASE_PASSWORD") ?? "vapor_password",
            database: Environment.get("DATABASE_NAME") ?? "vapor_database"
        ), as: .psql)
        
        // Configure AWS client
        app.aws.client = app.aws.clientOrInit
        
        return app
    }
    
    private static func processRetryRecord(
        record: SQSEvent.Message,
        app: Application,
        logger: Logger
    ) async throws {
        guard let body = record.body else {
            throw RetryError.invalidMessageBody
        }
        
        let retryEvent = try JSONDecoder().decode(SQSNotificationEvent.self, from: Data(body.utf8))
        
        logger.info("Processing retry for schedule \(retryEvent.scheduleId), attempt \(retryEvent.retryAttempt)")
        
        // Check if we've exceeded max retries
        if retryEvent.retryAttempt >= retryEvent.maxRetries {
            logger.error("Max retries exceeded for schedule \(retryEvent.scheduleId)")
            try await markScheduleAsPermanentlyFailed(scheduleId: retryEvent.scheduleId, app: app)
            return
        }
        
        // Calculate exponential backoff delay
        let backoffDelay = calculateBackoffDelay(attempt: retryEvent.retryAttempt)
        
        // Check if enough time has passed since the last attempt
        if let lastAttemptTime = getLastAttemptTime(from: record),
           Date().timeIntervalSince(lastAttemptTime) < TimeInterval(backoffDelay) {
            // Re-queue with delay
            try await requeueWithDelay(event: retryEvent, delay: backoffDelay, app: app)
            return
        }
        
        // Attempt to process the notification again
        do {
            try await retryNotificationDelivery(event: retryEvent, app: app, logger: logger)
            logger.info("Retry successful for schedule \(retryEvent.scheduleId)")
        } catch {
            logger.error("Retry failed for schedule \(retryEvent.scheduleId): \(error)")
            
            // Increment retry count and re-queue
            let nextRetryEvent = SQSNotificationEvent(
                scheduleId: retryEvent.scheduleId,
                entityType: retryEvent.entityType,
                entityId: retryEvent.entityId,
                userId: retryEvent.userId,
                reminderHoursBefore: retryEvent.reminderHoursBefore,
                originalError: retryEvent.originalError,
                retryAttempt: retryEvent.retryAttempt + 1,
                maxRetries: retryEvent.maxRetries,
                delaySeconds: calculateBackoffDelay(attempt: retryEvent.retryAttempt + 1)
            )
            
            try await sendToRetryQueue(event: nextRetryEvent, app: app)
        }
    }
    
    private static func retryNotificationDelivery(
        event: SQSNotificationEvent,
        app: Application,
        logger: Logger
    ) async throws {
        guard let scheduleUUID = UUID(uuidString: event.scheduleId) else {
            throw RetryError.invalidScheduleID
        }
        
        // Find the notification schedule
        guard let schedule = try await NotificationSchedule.find(scheduleUUID, on: app.db) else {
            throw RetryError.scheduleNotFound
        }
        
        // Get user notification preferences
        guard let preferences = try await UserNotificationPreferences.query(on: app.db)
            .filter(\.$user.$id == schedule.$user.id)
            .first() else {
            throw RetryError.preferencesNotFound
        }
        
        // Create notification payload
        let payload = NotificationPayload(
            title: generateNotificationTitle(for: schedule),
            message: generateNotificationMessage(for: schedule),
            entityType: EntityType(rawValue: schedule.entityType) ?? .task,
            entityID: schedule.entityID,
            userID: try schedule.$user.id,
            dueDate: schedule.dueDate,
            reminderHoursBefore: schedule.reminderHoursBefore,
            metadata: [
                "schedule_id": event.scheduleId,
                "entity_title": schedule.entityTitle,
                "retry_attempt": "\(event.retryAttempt)"
            ]
        )
        
        // Determine delivery channels (may be different from original attempt)
        let channels = getEnabledChannels(from: preferences)
        
        // Create a mock request for the notification service
        let mockRequest = try createMockRequest(app: app)
        
        // Attempt delivery
        let deliveryService = NotificationDeliveryService(awsClient: app.aws.client, logger: logger)
        let results = try await deliveryService.deliverNotification(payload, via: channels, on: mockRequest).get()
        
        // Check if any delivery was successful
        let hasSuccessfulDelivery = results.contains { $0.success }
        
        if hasSuccessfulDelivery {
            schedule.status = ScheduleStatus.sent.rawValue
            schedule.deliveryAttempts = (schedule.deliveryAttempts ?? 0) + 1
            try await schedule.update(on: app.db)
            logger.info("Retry successful for schedule \(event.scheduleId)")
        } else {
            throw RetryError.deliveryFailed
        }
    }
    
    private static func calculateBackoffDelay(attempt: Int) -> Int {
        // Exponential backoff: 2^attempt * 60 seconds, capped at 1 hour
        let delay = min(Int(pow(2.0, Double(attempt))) * 60, 3600)
        return delay
    }
    
    private static func getLastAttemptTime(from record: SQSEvent.Message) -> Date? {
        // Extract timestamp from SQS message attributes or approximate from message timestamp
        if let timestamp = record.attributes?["SentTimestamp"],
           let timestampDouble = Double(timestamp) {
            return Date(timeIntervalSince1970: timestampDouble / 1000.0)
        }
        return nil
    }
    
    private static func requeueWithDelay(
        event: SQSNotificationEvent,
        delay: Int,
        app: Application
    ) async throws {
        let sqsClient = SQS(client: app.aws.client, region: .useast1)
        let queueUrl = Environment.get("NOTIFICATION_RETRY_QUEUE_URL") ?? ""
        
        let messageBody = try JSONEncoder().encode(event)
        
        let sendMessageInput = SQS.SendMessageRequest(
            delaySeconds: delay,
            messageBody: String(data: messageBody, encoding: .utf8) ?? "",
            queueUrl: queueUrl
        )
        
        _ = try await sqsClient.sendMessage(sendMessageInput)
    }
    
    private static func sendToRetryQueue(
        event: SQSNotificationEvent,
        app: Application
    ) async throws {
        let sqsClient = SQS(client: app.aws.client, region: .useast1)
        let queueUrl = Environment.get("NOTIFICATION_RETRY_QUEUE_URL") ?? ""
        
        let messageBody = try JSONEncoder().encode(event)
        
        let sendMessageInput = SQS.SendMessageRequest(
            delaySeconds: event.delaySeconds,
            messageBody: String(data: messageBody, encoding: .utf8) ?? "",
            queueUrl: queueUrl
        )
        
        _ = try await sqsClient.sendMessage(sendMessageInput)
    }
    
    private static func markScheduleAsPermanentlyFailed(
        scheduleId: String,
        app: Application
    ) async throws {
        guard let scheduleUUID = UUID(uuidString: scheduleId) else { return }
        
        guard let schedule = try await NotificationSchedule.find(scheduleUUID, on: app.db) else { return }
        
        schedule.status = ScheduleStatus.failed.rawValue
        schedule.lastError = "Max retry attempts exceeded"
        try await schedule.update(on: app.db)
    }
    
    private static func generateNotificationTitle(for schedule: NotificationSchedule) -> String {
        let entityType = schedule.entityType.capitalized
        return "\(entityType) Reminder"
    }
    
    private static func generateNotificationMessage(for schedule: NotificationSchedule) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        
        return "Reminder: Your \(schedule.entityType) '\(schedule.entityTitle)' is due on \(formatter.string(from: schedule.dueDate))."
    }
    
    private static func getEnabledChannels(from preferences: UserNotificationPreferences) -> [DeliveryChannel] {
        var channels: [DeliveryChannel] = []
        
        if preferences.smsEnabled && preferences.smsPhoneNumber != nil {
            channels.append(.sms)
        }
        
        if preferences.emailEnabled && preferences.emailAddress != nil {
            channels.append(.email)
        }
        
        if preferences.pushEnabled {
            channels.append(.push)
        }
        
        if preferences.browserEnabled {
            channels.append(.browser)
        }
        
        if preferences.inAppEnabled {
            channels.append(.inApp)
        }
        
        return channels
    }
    
    private static func createMockRequest(app: Application) throws -> Request {
        let headers = HTTPHeaders()
        let body = ByteBuffer()
        
        return Request(
            application: app,
            method: .POST,
            url: URI(string: "/lambda/retry"),
            headers: headers,
            collectedBody: body,
            on: app.eventLoopGroup.next()
        )
    }
}

// MARK: - Error Types
enum RetryError: Error, LocalizedError {
    case invalidMessageBody
    case invalidScheduleID
    case scheduleNotFound
    case preferencesNotFound
    case deliveryFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidMessageBody:
            return "Invalid SQS message body"
        case .invalidScheduleID:
            return "Invalid schedule ID format"
        case .scheduleNotFound:
            return "Notification schedule not found"
        case .preferencesNotFound:
            return "User notification preferences not found"
        case .deliveryFailed:
            return "Notification delivery failed after retry"
        }
    }
}
