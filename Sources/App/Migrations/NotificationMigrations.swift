//
//  NotificationMigrations.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Fluent
import Vapor

// MARK: - User Notification Preferences Migration
struct UserNotificationPreferencesMigration: AsyncMigration {
    func prepare(on database: Database) async throws {
        try await database.schema(UserNotificationPreferences.schema)
            .id()
            .field("user_id", .uuid, .required, .references(User.schema, "id", onDelete: .cascade))
            .field("sms_enabled", .bool, .required)
            .field("sms_phone_number", .string)
            .field("email_enabled", .bool, .required)
            .field("email_address", .string)
            .field("push_enabled", .bool, .required)
            .field("browser_enabled", .bool, .required)
            .field("in_app_enabled", .bool, .required)
            .field("reminder_hours", .array(of: .int), .required)
            .field("quiet_hours_start", .int)
            .field("quiet_hours_end", .int)
            .field("timezone", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .unique(on: "user_id")
            .create()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(UserNotificationPreferences.schema).delete()
    }
}

// MARK: - Notification Schedule Migration
struct NotificationScheduleMigration: AsyncMigration {
    func prepare(on database: Database) async throws {
        try await database.schema(NotificationSchedule.schema)
            .id()
            .field("user_id", .uuid, .required, .references(User.schema, "id", onDelete: .cascade))
            .field("entity_type", .string, .required)
            .field("entity_id", .uuid, .required)
            .field("entity_title", .string, .required)
            .field("due_date", .datetime, .required)
            .field("reminder_hours_before", .int, .required)
            .field("scheduled_time", .datetime, .required)
            .field("eventbridge_schedule_name", .string)
            .field("eventbridge_schedule_arn", .string)
            .field("status", .string, .required)
            .field("delivery_attempts", .int)
            .field("last_error", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(NotificationSchedule.schema).delete()
    }
}

// MARK: - Enhanced User Notification Migration
struct EnhancedUserNotificationMigration: AsyncMigration {
    func prepare(on database: Database) async throws {
        try await database.schema(UserNotification.schema)
            .field("entity_type", .string)
            .field("entity_id", .uuid)
            .field("delivery_channel", .string)
            .field("delivery_status", .string)
            .field("delivery_error", .string)
            .field("scheduled_for", .datetime)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(UserNotification.schema)
            .deleteField("entity_type")
            .deleteField("entity_id")
            .deleteField("delivery_channel")
            .deleteField("delivery_status")
            .deleteField("delivery_error")
            .deleteField("scheduled_for")
            .update()
    }
}

// MARK: - Default Notification Preferences Migration
struct DefaultNotificationPreferencesMigration: AsyncMigration {
    func prepare(on database: Database) async throws {
        // Create default notification preferences for all existing users
        let users = try await User.query(on: database).all()
        
        for user in users {
            guard let userID = user.id else { continue }
            
            // Check if preferences already exist
            let existingPreferences = try await UserNotificationPreferences.query(on: database)
                .filter(\.$user.$id == userID)
                .first()
            
            if existingPreferences == nil {
                let preferences = UserNotificationPreferences(
                    userID: userID,
                    smsEnabled: true,
                    smsPhoneNumber: nil,
                    emailEnabled: true,
                    emailAddress: user.email,
                    pushEnabled: true,
                    browserEnabled: true,
                    inAppEnabled: true,
                    reminderHours: [24, 48],
                    quietHoursStart: nil,
                    quietHoursEnd: nil,
                    timezone: "America/New_York"
                )
                
                try await preferences.save(on: database)
            }
        }
    }
    
    func revert(on database: Database) async throws {
        // Remove all default preferences created by this migration
        try await UserNotificationPreferences.query(on: database).delete()
    }
}

// MARK: - Notification Template Migration
struct NotificationTemplateMigration: AsyncMigration {
    func prepare(on database: Database) async throws {
        try await database.schema("notification_templates")
            .id()
            .field("template_key", .string, .required)
            .field("entity_type", .string, .required)
            .field("delivery_channel", .string, .required)
            .field("subject_template", .string)
            .field("body_template", .string, .required)
            .field("variables", .array(of: .string))
            .field("is_active", .bool, .required)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .unique(on: "template_key", "entity_type", "delivery_channel")
            .create()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema("notification_templates").delete()
    }
}

// MARK: - Notification Delivery Log Migration
struct NotificationDeliveryLogMigration: AsyncMigration {
    func prepare(on database: Database) async throws {
        try await database.schema("notification_delivery_logs")
            .id()
            .field("notification_id", .uuid, .required, .references(UserNotification.schema, "id", onDelete: .cascade))
            .field("delivery_channel", .string, .required)
            .field("delivery_status", .string, .required)
            .field("delivery_attempt", .int, .required)
            .field("delivery_time", .datetime)
            .field("error_message", .string)
            .field("response_data", .json)
            .field("created_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema("notification_delivery_logs").delete()
    }
}

// MARK: - Indexes Migration for Performance
struct NotificationIndexesMigration: AsyncMigration {
    func prepare(on database: Database) async throws {
        // Add indexes for better query performance
        try await database.schema(UserNotification.schema)
            .createIndex(on: "user_id")
            .createIndex(on: "kind")
            .createIndex(on: "delivery_status")
            .createIndex(on: "entity_type", "entity_id")
            .createIndex(on: "scheduled_for")
            .update()
        
        try await database.schema(NotificationSchedule.schema)
            .createIndex(on: "user_id")
            .createIndex(on: "entity_type", "entity_id")
            .createIndex(on: "status")
            .createIndex(on: "scheduled_time")
            .update()
        
        try await database.schema(UserNotificationPreferences.schema)
            .createIndex(on: "user_id")
            .update()
    }
    
    func revert(on database: Database) async throws {
        // Note: Dropping indexes in revert is optional as they don't affect data
        // but can be added if needed for complete rollback
    }
}
