//
//  File.swift
//
//
//  Created by <PERSON> on 1/2/24.
//

import Foundation
import Fluent
import Vapor

final class UserNotification: Model, Content, @unchecked Sendable {
    static let schema = "notifications"

    @ID var id: UUID?

    @Field(key: "title")
    var title: String

    /// standard | chat | urgent | reminder
    @Field(key: "kind")
    var kind: String

    @Field(key: "message")
    var message: String

    @Field(key: "read")
    var read: Bool

    @Field(key: "user_id")
    var userID: UUID

    @OptionalField(key: "meta")
    var meta: MetaData?

    // Reference to the entity that triggered this notification
    @OptionalField(key: "entity_type")
    var entityType: String?

    @OptionalField(key: "entity_id")
    var entityID: UUID?

    // Delivery channel used for this notification
    @OptionalField(key: "delivery_channel")
    var deliveryChannel: String?

    // Delivery status: pending, sent, failed, retrying
    @OptionalField(key: "delivery_status")
    var deliveryStatus: String?

    @OptionalField(key: "delivery_error")
    var deliveryError: String?

    @OptionalField(key: "scheduled_for")
    var scheduledFor: Date?

    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?

    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?

    init() { }

    init(id:        UUID?    = nil,
         title:     String,
         kind:      String,
         message:   String,
         read:      Bool,
         userID:    UUID,
         meta:      MetaData? = nil,
         entityType: String? = nil,
         entityID: UUID? = nil,
         deliveryChannel: String? = nil,
         deliveryStatus: String? = nil,
         scheduledFor: Date? = nil
         ) {
        self.id          = id
        self.title       = title
        self.kind        = kind
        self.message     = message
        self.read        = read
        self.userID      = userID
        self.meta        = meta
        self.entityType  = entityType
        self.entityID    = entityID
        self.deliveryChannel = deliveryChannel
        self.deliveryStatus = deliveryStatus
        self.scheduledFor = scheduledFor
    }
}

// MARK: - User Notification Preferences Model
final class UserNotificationPreferences: Model, Content, @unchecked Sendable {
    static let schema = "user_notification_preferences"

    @ID var id: UUID?

    @Parent(key: "user_id")
    var user: User

    // SMS Preferences
    @Field(key: "sms_enabled")
    var smsEnabled: Bool

    @OptionalField(key: "sms_phone_number")
    var smsPhoneNumber: String?

    // Email Preferences
    @Field(key: "email_enabled")
    var emailEnabled: Bool

    @OptionalField(key: "email_address")
    var emailAddress: String?

    // Push Notification Preferences
    @Field(key: "push_enabled")
    var pushEnabled: Bool

    // Browser Notification Preferences
    @Field(key: "browser_enabled")
    var browserEnabled: Bool

    // In-App Notification Preferences
    @Field(key: "in_app_enabled")
    var inAppEnabled: Bool

    // Reminder Timing Preferences (in hours before due date)
    @Field(key: "reminder_hours")
    var reminderHours: [Int] // Default: [24, 48]

    // Quiet Hours (24-hour format)
    @OptionalField(key: "quiet_hours_start")
    var quietHoursStart: Int? // e.g., 22 for 10 PM

    @OptionalField(key: "quiet_hours_end")
    var quietHoursEnd: Int? // e.g., 8 for 8 AM

    @OptionalField(key: "timezone")
    var timezone: String? // e.g., "America/New_York"

    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?

    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?

    init() { }

    init(id: UUID? = nil,
         userID: UUID,
         smsEnabled: Bool = true,
         smsPhoneNumber: String? = nil,
         emailEnabled: Bool = true,
         emailAddress: String? = nil,
         pushEnabled: Bool = true,
         browserEnabled: Bool = true,
         inAppEnabled: Bool = true,
         reminderHours: [Int] = [24, 48],
         quietHoursStart: Int? = nil,
         quietHoursEnd: Int? = nil,
         timezone: String? = nil) {
        self.id = id
        self.$user.id = userID
        self.smsEnabled = smsEnabled
        self.smsPhoneNumber = smsPhoneNumber
        self.emailEnabled = emailEnabled
        self.emailAddress = emailAddress
        self.pushEnabled = pushEnabled
        self.browserEnabled = browserEnabled
        self.inAppEnabled = inAppEnabled
        self.reminderHours = reminderHours
        self.quietHoursStart = quietHoursStart
        self.quietHoursEnd = quietHoursEnd
        self.timezone = timezone
    }
}

// MARK: - Notification Schedule Model
final class NotificationSchedule: Model, Content, @unchecked Sendable {
    static let schema = "notification_schedules"

    @ID var id: UUID?

    @Parent(key: "user_id")
    var user: User

    // Entity information
    @Field(key: "entity_type")
    var entityType: String // "task", "goal", "intervention", "follow_up"

    @Field(key: "entity_id")
    var entityID: UUID

    @Field(key: "entity_title")
    var entityTitle: String

    @Field(key: "due_date")
    var dueDate: Date

    // Notification timing
    @Field(key: "reminder_hours_before")
    var reminderHoursBefore: Int // 24 or 48

    @Field(key: "scheduled_time")
    var scheduledTime: Date

    // AWS EventBridge Schedule information
    @OptionalField(key: "eventbridge_schedule_name")
    var eventBridgeScheduleName: String?

    @OptionalField(key: "eventbridge_schedule_arn")
    var eventBridgeScheduleArn: String?

    // Status: scheduled, sent, failed, cancelled
    @Field(key: "status")
    var status: String

    @OptionalField(key: "delivery_attempts")
    var deliveryAttempts: Int?

    @OptionalField(key: "last_error")
    var lastError: String?

    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?

    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?

    init() { }

    init(id: UUID? = nil,
         userID: UUID,
         entityType: String,
         entityID: UUID,
         entityTitle: String,
         dueDate: Date,
         reminderHoursBefore: Int,
         scheduledTime: Date,
         status: String = "scheduled") {
        self.id = id
        self.$user.id = userID
        self.entityType = entityType
        self.entityID = entityID
        self.entityTitle = entityTitle
        self.dueDate = dueDate
        self.reminderHoursBefore = reminderHoursBefore
        self.scheduledTime = scheduledTime
        self.status = status
        self.deliveryAttempts = 0
    }
}

// MARK: - Enums and Constants
enum NotificationKind: String, CaseIterable {
    case standard = "standard"
    case chat = "chat"
    case urgent = "urgent"
    case reminder = "reminder"
}

enum DeliveryChannel: String, CaseIterable {
    case sms = "sms"
    case email = "email"
    case push = "push"
    case browser = "browser"
    case inApp = "in_app"
}

enum DeliveryStatus: String, CaseIterable {
    case pending = "pending"
    case sent = "sent"
    case failed = "failed"
    case retrying = "retrying"
}

enum EntityType: String, CaseIterable {
    case task = "task"
    case goal = "goal"
    case intervention = "intervention"
    case followUp = "follow_up"
}

enum ScheduleStatus: String, CaseIterable {
    case scheduled = "scheduled"
    case sent = "sent"
    case failed = "failed"
    case cancelled = "cancelled"
}


struct NotificationMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(UserNotification.schema)
            .id()
            .field("title",       .string, .required)
            .field("kind",        .string, .required)
            .field("message",     .string, .required)
            .field("read",        .bool,   .required, .sql(.default(false)))
            .field("user_id",     .uuid, .required)
            .field("meta",        .json)
                    
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(UserNotification.schema).delete()
    }
}
