//
//  NotificationTemplate.swift
//  
//
//  Created by <PERSON> on 7/24/25.
//

import Foundation
import Fluent
import Vapor

// MARK: - Notification Template Model
final class NotificationTemplate: Model, Content, @unchecked Sendable {
    static let schema = "notification_templates"
    
    @ID var id: UUID?
    
    @Field(key: "template_key")
    var templateKey: String
    
    @Field(key: "entity_type")
    var entityType: String
    
    @Field(key: "delivery_channel")
    var deliveryChannel: String
    
    @OptionalField(key: "subject_template")
    var subjectTemplate: String?
    
    @Field(key: "body_template")
    var bodyTemplate: String
    
    @Field(key: "variables")
    var variables: [String]
    
    @Field(key: "is_active")
    var isActive: Bool
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil,
         templateKey: String,
         entityType: String,
         deliveryChannel: String,
         subjectTemplate: String? = nil,
         bodyTemplate: String,
         variables: [String] = [],
         isActive: Bool = true) {
        self.id = id
        self.templateKey = templateKey
        self.entityType = entityType
        self.deliveryChannel = deliveryChannel
        self.subjectTemplate = subjectTemplate
        self.bodyTemplate = bodyTemplate
        self.variables = variables
        self.isActive = isActive
    }
}

// MARK: - Template Variables
struct TemplateVariables {
    let entityTitle: String
    let entityType: String
    let dueDate: Date
    let reminderHours: Int
    let userName: String?
    let userEmail: String?
    let appUrl: String
    let organizationName: String?
    
    // Computed properties for common formats
    var dueDateFormatted: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: dueDate)
    }
    
    var dueDateShort: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter.string(from: dueDate)
    }
    
    var timeUntilDue: String {
        return reminderHours == 24 ? "tomorrow" : "in \(reminderHours) hours"
    }
    
    var entityTypeCapitalized: String {
        return entityType.capitalized
    }
    
    // Convert to dictionary for template rendering
    func toDictionary() -> [String: String] {
        return [
            "entity_title": entityTitle,
            "entity_type": entityType,
            "entity_type_capitalized": entityTypeCapitalized,
            "due_date": dueDateFormatted,
            "due_date_short": dueDateShort,
            "reminder_hours": "\(reminderHours)",
            "time_until_due": timeUntilDue,
            "user_name": userName ?? "",
            "user_email": userEmail ?? "",
            "app_url": appUrl,
            "organization_name": organizationName ?? "Wellup"
        ]
    }
}

// MARK: - Template Renderer
struct NotificationTemplateRenderer {
    
    static func render(template: NotificationTemplate, variables: TemplateVariables) -> RenderedTemplate {
        let variableDict = variables.toDictionary()
        
        let renderedSubject = template.subjectTemplate.map { 
            renderString($0, with: variableDict) 
        }
        
        let renderedBody = renderString(template.bodyTemplate, with: variableDict)
        
        return RenderedTemplate(
            subject: renderedSubject,
            body: renderedBody,
            templateKey: template.templateKey,
            deliveryChannel: template.deliveryChannel
        )
    }
    
    private static func renderString(_ template: String, with variables: [String: String]) -> String {
        var rendered = template
        
        for (key, value) in variables {
            // Replace {{variable}} patterns
            rendered = rendered.replacingOccurrences(of: "{{\(key)}}", with: value)
            // Replace {variable} patterns
            rendered = rendered.replacingOccurrences(of: "{\(key)}", with: value)
        }
        
        return rendered
    }
}

// MARK: - Rendered Template
struct RenderedTemplate {
    let subject: String?
    let body: String
    let templateKey: String
    let deliveryChannel: String
}

// MARK: - Default Templates
struct DefaultNotificationTemplates {
    
    static let templates: [NotificationTemplate] = [
        // Task Templates
        NotificationTemplate(
            templateKey: "task_reminder_sms",
            entityType: "task",
            deliveryChannel: "sms",
            bodyTemplate: "Reminder: Your {entity_type} '{entity_title}' is due {time_until_due} ({due_date_short}). Reply STOP to opt out.",
            variables: ["entity_title", "entity_type", "due_date_short", "time_until_due"]
        ),
        
        NotificationTemplate(
            templateKey: "task_reminder_email",
            entityType: "task",
            deliveryChannel: "email",
            subjectTemplate: "{entity_type_capitalized} Reminder: {entity_title}",
            bodyTemplate: """
            Hello {user_name},
            
            This is a reminder that your {entity_type} "{entity_title}" is due {time_until_due}.
            
            Due Date: {due_date}
            
            Please log in to {app_url} to view details and mark as complete.
            
            Best regards,
            The {organization_name} Team
            """,
            variables: ["user_name", "entity_type", "entity_title", "time_until_due", "due_date", "app_url", "organization_name"]
        ),
        
        NotificationTemplate(
            templateKey: "task_reminder_push",
            entityType: "task",
            deliveryChannel: "push",
            subjectTemplate: "{entity_type_capitalized} Due {time_until_due}",
            bodyTemplate: "Your {entity_type} '{entity_title}' is due {time_until_due}",
            variables: ["entity_type", "entity_type_capitalized", "entity_title", "time_until_due"]
        ),
        
        // Goal Templates
        NotificationTemplate(
            templateKey: "goal_reminder_sms",
            entityType: "goal",
            deliveryChannel: "sms",
            bodyTemplate: "Goal reminder: '{entity_title}' target date is {time_until_due} ({due_date_short}). Stay on track!",
            variables: ["entity_title", "due_date_short", "time_until_due"]
        ),
        
        NotificationTemplate(
            templateKey: "goal_reminder_email",
            entityType: "goal",
            deliveryChannel: "email",
            subjectTemplate: "Goal Reminder: {entity_title}",
            bodyTemplate: """
            Hello {user_name},
            
            This is a reminder about your goal "{entity_title}" with a target date {time_until_due}.
            
            Target Date: {due_date}
            
            Keep up the great work! Log in to {app_url} to track your progress.
            
            Best regards,
            The {organization_name} Team
            """,
            variables: ["user_name", "entity_title", "time_until_due", "due_date", "app_url", "organization_name"]
        ),
        
        // Intervention Templates
        NotificationTemplate(
            templateKey: "intervention_reminder_sms",
            entityType: "intervention",
            deliveryChannel: "sms",
            bodyTemplate: "Intervention reminder: '{entity_title}' is due {time_until_due} ({due_date_short}).",
            variables: ["entity_title", "due_date_short", "time_until_due"]
        ),
        
        NotificationTemplate(
            templateKey: "intervention_reminder_email",
            entityType: "intervention",
            deliveryChannel: "email",
            subjectTemplate: "Intervention Reminder: {entity_title}",
            bodyTemplate: """
            Hello {user_name},
            
            This is a reminder about the intervention "{entity_title}" that is due {time_until_due}.
            
            Due Date: {due_date}
            
            Please review and complete this intervention in {app_url}.
            
            Best regards,
            The {organization_name} Team
            """,
            variables: ["user_name", "entity_title", "time_until_due", "due_date", "app_url", "organization_name"]
        ),
        
        // Follow-up Templates
        NotificationTemplate(
            templateKey: "follow_up_reminder_sms",
            entityType: "follow_up",
            deliveryChannel: "sms",
            bodyTemplate: "Follow-up reminder: '{entity_title}' is scheduled for {time_until_due} ({due_date_short}).",
            variables: ["entity_title", "due_date_short", "time_until_due"]
        ),
        
        NotificationTemplate(
            templateKey: "follow_up_reminder_email",
            entityType: "follow_up",
            deliveryChannel: "email",
            subjectTemplate: "Follow-up Reminder: {entity_title}",
            bodyTemplate: """
            Hello {user_name},
            
            This is a reminder about your follow-up "{entity_title}" scheduled for {time_until_due}.
            
            Scheduled Date: {due_date}
            
            Please prepare for your follow-up appointment. Visit {app_url} for more details.
            
            Best regards,
            The {organization_name} Team
            """,
            variables: ["user_name", "entity_title", "time_until_due", "due_date", "app_url", "organization_name"]
        ),
        
        // Generic Templates
        NotificationTemplate(
            templateKey: "generic_reminder_in_app",
            entityType: "generic",
            deliveryChannel: "in_app",
            subjectTemplate: "{entity_type_capitalized} Reminder",
            bodyTemplate: "Your {entity_type} '{entity_title}' is due {time_until_due}.",
            variables: ["entity_type", "entity_type_capitalized", "entity_title", "time_until_due"]
        ),
        
        NotificationTemplate(
            templateKey: "generic_reminder_browser",
            entityType: "generic",
            deliveryChannel: "browser",
            subjectTemplate: "{entity_type_capitalized} Due {time_until_due}",
            bodyTemplate: "Your {entity_type} '{entity_title}' is due {time_until_due}. Click to view details.",
            variables: ["entity_type", "entity_type_capitalized", "entity_title", "time_until_due"]
        )
    ]
}
